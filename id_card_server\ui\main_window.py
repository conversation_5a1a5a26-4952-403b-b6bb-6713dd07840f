#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
"""

import sys
import threading
import logging
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QTextEdit, QGroupBox,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QSplitter, QFrame, QMessageBox, QProgressBar,
                             QStatusBar, QMenuBar, QAction, QFileDialog,
                             QTabWidget, QGridLayout, QDialog, QMenu,
                             QButtonGroup, QRadioButton, QInputDialog)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QIcon

from core.id_card_reader import IDCardReader
from core.database import DatabaseManager
from core.task_manager import TaskManager
from core.photo_exporter import PhotoExporter
from core.broadcast_service import BroadcastService
from ui.export_dialog import ExportDialog
from ui.station_display_window import StationDisplayWindow
from ui.station_manager_dialog import StationManagerDialog
import config

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"{config.APP_NAME} v{config.APP_VERSION}")
        self.setGeometry(100, 100, config.WINDOW_WIDTH, config.WINDOW_HEIGHT)
        
        # 核心组件
        self.db = DatabaseManager(config.DATABASE_PATH)
        self.reader = IDCardReader()
        self.task_manager = TaskManager(self.db, config.SERVER_PORT)
        self.exporter = PhotoExporter(self.db)
        self.broadcast_service = BroadcastService(config.SERVER_PORT)
        
        # 机位号显示窗口
        self.station_display = StationDisplayWindow()
        self.station_display.window_closed.connect(self.on_station_window_closed)

        # 状态变量
        self.reader_connected = False
        self.server_running = False
        self.auto_read_enabled = False

        # 导出线程
        self.export_thread = None
        
        self.init_ui()
        self.setup_connections()
        self.setup_timer()

        # 加载现有数据
        self.update_task_table()
        self.update_statistics()

        # 尝试连接读卡器
        self.connect_reader()
    
    def init_ui(self):
        """初始化界面"""
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # 右侧信息面板
        right_panel = self.create_info_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        export_action = QAction('导出照片(&E)', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.show_export_dialog)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        connect_reader_action = QAction('连接读卡器(&C)', self)
        connect_reader_action.triggered.connect(self.connect_reader)
        tools_menu.addAction(connect_reader_action)
        
        start_server_action = QAction('启动服务器(&S)', self)
        start_server_action.triggered.connect(self.start_server)
        tools_menu.addAction(start_server_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 读卡器状态
        self.reader_status_label = QLabel("读卡器: 未连接")
        self.status_bar.addWidget(self.reader_status_label)
        
        # 服务器状态
        self.server_status_label = QLabel("服务器: 未启动")
        self.status_bar.addWidget(self.server_status_label)
        
        # 任务统计
        self.task_stats_label = QLabel("任务: 0/0/0")
        self.status_bar.addPermanentWidget(self.task_stats_label)
    
    def create_control_panel(self):
        """创建控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        
        # 读卡器控制组
        reader_group = QGroupBox("读卡器控制")
        reader_layout = QVBoxLayout(reader_group)
        
        self.reader_status_text = QLabel("状态: 未连接")
        self.reader_status_text.setStyleSheet("color: red; font-weight: bold;")
        reader_layout.addWidget(self.reader_status_text)
        
        self.connect_reader_btn = QPushButton("连接读卡器")
        self.connect_reader_btn.clicked.connect(self.connect_reader)
        reader_layout.addWidget(self.connect_reader_btn)
        
        self.auto_read_btn = QPushButton("启用自动读卡")
        self.auto_read_btn.setEnabled(False)
        self.auto_read_btn.clicked.connect(self.toggle_auto_read)
        reader_layout.addWidget(self.auto_read_btn)

        self.station_display_btn = QPushButton("显示机位号窗口")
        self.station_display_btn.clicked.connect(self.toggle_station_display)
        self.station_display_btn.setStyleSheet("background-color: #3498db; color: white;")
        reader_layout.addWidget(self.station_display_btn)
        
        layout.addWidget(reader_group)
        
        # 服务器控制组
        server_group = QGroupBox("服务器控制")
        server_layout = QVBoxLayout(server_group)
        
        self.server_status_text = QLabel("状态: 未启动")
        self.server_status_text.setStyleSheet("color: red; font-weight: bold;")
        server_layout.addWidget(self.server_status_text)
        
        self.start_server_btn = QPushButton("启动服务器")
        self.start_server_btn.clicked.connect(self.start_server)
        server_layout.addWidget(self.start_server_btn)
        
        self.stop_server_btn = QPushButton("停止服务器")
        self.stop_server_btn.setEnabled(False)
        self.stop_server_btn.clicked.connect(self.stop_server)
        server_layout.addWidget(self.stop_server_btn)
        
        layout.addWidget(server_group)
        
        # 导出控制组
        export_group = QGroupBox("导出控制")
        export_layout = QVBoxLayout(export_group)
        
        self.export_photos_btn = QPushButton("导出照片")
        self.export_photos_btn.clicked.connect(self.show_export_dialog)
        export_layout.addWidget(self.export_photos_btn)
        
        self.export_list_btn = QPushButton("导出任务列表")
        self.export_list_btn.clicked.connect(self.export_task_list)
        export_layout.addWidget(self.export_list_btn)

        layout.addWidget(export_group)

        # 任务管理组
        task_group = QGroupBox("任务管理")
        task_layout = QVBoxLayout(task_group)

        self.delete_task_btn = QPushButton("删除选中任务")
        self.delete_task_btn.clicked.connect(self.delete_selected_tasks)
        task_layout.addWidget(self.delete_task_btn)

        self.delete_all_pending_btn = QPushButton("删除所有未完成任务")
        self.delete_all_pending_btn.clicked.connect(self.delete_all_pending_tasks)
        self.delete_all_pending_btn.setStyleSheet("background-color: #ff6b6b; color: white;")
        task_layout.addWidget(self.delete_all_pending_btn)

        # 清空数据按钮
        self.clear_all_data_btn = QPushButton("清空所有数据")
        self.clear_all_data_btn.clicked.connect(self.clear_all_data)
        self.clear_all_data_btn.setStyleSheet("background-color: #e74c3c; color: white; font-weight: bold;")
        task_layout.addWidget(self.clear_all_data_btn)

        # 清空机位号分配按钮
        self.clear_station_assignments_btn = QPushButton("清空机位号分配")
        self.clear_station_assignments_btn.clicked.connect(self.clear_station_assignments)
        self.clear_station_assignments_btn.setStyleSheet("background-color: #f39c12; color: white;")
        task_layout.addWidget(self.clear_station_assignments_btn)

        # 测试任务生成按钮
        self.generate_test_tasks_btn = QPushButton("生成测试任务")
        self.generate_test_tasks_btn.clicked.connect(self.generate_test_tasks)
        self.generate_test_tasks_btn.setStyleSheet("background-color: #3498db; color: white;")
        task_layout.addWidget(self.generate_test_tasks_btn)

        # 任务来源选择
        task_source_layout = QHBoxLayout()
        task_source_label = QLabel("任务来源:")
        task_source_layout.addWidget(task_source_label)

        self.task_source_group = QButtonGroup()

        self.formal_source_btn = QRadioButton("正式任务")
        self.formal_source_btn.setChecked(True)  # 默认选中正式任务
        self.formal_source_btn.toggled.connect(self.on_task_source_changed)
        self.task_source_group.addButton(self.formal_source_btn, 0)
        task_source_layout.addWidget(self.formal_source_btn)

        self.test_source_btn = QRadioButton("测试任务")
        self.test_source_btn.toggled.connect(self.on_task_source_changed)
        self.task_source_group.addButton(self.test_source_btn, 1)
        task_source_layout.addWidget(self.test_source_btn)

        task_source_layout.addStretch()
        task_layout.addLayout(task_source_layout)

        layout.addWidget(task_group)

        # 照片参数组
        photo_group = QGroupBox("照片参数")
        photo_layout = QVBoxLayout(photo_group)

        self.photo_settings_btn = QPushButton("照片参数设置")
        self.photo_settings_btn.clicked.connect(self.open_photo_settings)
        photo_layout.addWidget(self.photo_settings_btn)

        layout.addWidget(photo_group)
        
        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QGridLayout(stats_group)
        
        self.total_tasks_label = QLabel("总任务: 0")
        self.pending_tasks_label = QLabel("待处理: 0")
        self.assigned_tasks_label = QLabel("已分配: 0")
        self.completed_tasks_label = QLabel("已完成: 0")
        
        stats_layout.addWidget(self.total_tasks_label, 0, 0)
        stats_layout.addWidget(self.pending_tasks_label, 0, 1)
        stats_layout.addWidget(self.assigned_tasks_label, 1, 0)
        stats_layout.addWidget(self.completed_tasks_label, 1, 1)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
        
        return panel
    
    def create_info_panel(self):
        """创建信息面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 日志选项卡
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)
        
        log_label = QLabel("系统日志")
        log_label.setFont(QFont("Arial", 10, QFont.Bold))
        log_layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # 限制日志行数（PyQt5中使用document().setMaximumBlockCount）
        self.log_text.document().setMaximumBlockCount(1000)
        log_layout.addWidget(self.log_text)
        
        tab_widget.addTab(log_tab, "系统日志")
        
        # 任务列表选项卡
        task_tab = QWidget()
        task_layout = QVBoxLayout(task_tab)

        # 表选择区域
        table_select_layout = QHBoxLayout()

        task_label = QLabel("任务列表")
        task_label.setFont(QFont("Arial", 10, QFont.Bold))
        table_select_layout.addWidget(task_label)

        table_select_layout.addStretch()

        # 表选择按钮组
        self.table_button_group = QButtonGroup()

        self.formal_table_btn = QRadioButton("正式任务")
        self.formal_table_btn.setChecked(True)  # 默认选中正式任务
        self.formal_table_btn.toggled.connect(self.on_table_selection_changed)
        self.table_button_group.addButton(self.formal_table_btn, 0)
        table_select_layout.addWidget(self.formal_table_btn)

        self.test_table_btn = QRadioButton("测试任务")
        self.test_table_btn.toggled.connect(self.on_table_selection_changed)
        self.table_button_group.addButton(self.test_table_btn, 1)
        table_select_layout.addWidget(self.test_table_btn)

        task_layout.addLayout(table_select_layout)
        
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(6)
        self.task_table.setHorizontalHeaderLabels([
            "任务ID", "姓名", "身份证号", "状态", "创建时间", "PC端ID"
        ])
        
        # 设置表格属性
        header = self.task_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.task_table.setAlternatingRowColors(True)
        self.task_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.task_table.setSelectionMode(QTableWidget.MultiSelection)  # 启用多选
        self.task_table.setContextMenuPolicy(Qt.CustomContextMenu)  # 启用右键菜单
        self.task_table.customContextMenuRequested.connect(self.show_task_context_menu)

        task_layout.addWidget(self.task_table)

        # 任务操作按钮
        task_btn_layout = QHBoxLayout()

        self.reassign_task_btn = QPushButton("重新分配选中任务")
        self.reassign_task_btn.clicked.connect(self.reassign_selected_tasks)
        self.reassign_task_btn.setStyleSheet("background-color: #e74c3c; color: white; padding: 8px;")
        task_btn_layout.addWidget(self.reassign_task_btn)

        self.delete_task_btn = QPushButton("删除选中任务")
        self.delete_task_btn.clicked.connect(self.delete_selected_tasks)
        self.delete_task_btn.setStyleSheet("background-color: #95a5a6; color: white; padding: 8px;")
        task_btn_layout.addWidget(self.delete_task_btn)

        task_btn_layout.addStretch()
        task_layout.addLayout(task_btn_layout)

        tab_widget.addTab(task_tab, "任务列表")
        
        # PC端连接选项卡
        client_tab = QWidget()
        client_layout = QVBoxLayout(client_tab)
        
        client_label = QLabel("PC端连接")
        client_label.setFont(QFont("Arial", 10, QFont.Bold))
        client_layout.addWidget(client_label)
        
        self.client_table = QTableWidget()
        self.client_table.setColumnCount(5)
        self.client_table.setHorizontalHeaderLabels([
            "客户端ID", "IP地址", "机位号", "连接时间", "任务数(未完成/总数)"
        ])
        
        # 设置表格属性
        client_header = self.client_table.horizontalHeader()
        client_header.setSectionResizeMode(QHeaderView.Stretch)
        self.client_table.setAlternatingRowColors(True)
        
        client_layout.addWidget(self.client_table)

        # 客户端管理按钮
        client_btn_layout = QHBoxLayout()

        self.station_manager_btn = QPushButton("机位号管理")
        self.station_manager_btn.clicked.connect(self.show_station_manager)
        self.station_manager_btn.setStyleSheet("background-color: #9b59b6; color: white; padding: 8px;")
        client_btn_layout.addWidget(self.station_manager_btn)

        client_btn_layout.addStretch()
        client_layout.addLayout(client_btn_layout)

        tab_widget.addTab(client_tab, "PC端连接")

        return panel

    def setup_connections(self):
        """设置信号连接"""
        # 任务管理器信号
        self.task_manager.client_connected.connect(self.on_client_connected)
        self.task_manager.client_disconnected.connect(self.on_client_disconnected)
        self.task_manager.task_assigned.connect(self.on_task_assigned)
        self.task_manager.photo_uploaded.connect(self.on_photo_uploaded)
        self.task_manager.task_cancelled.connect(self.on_task_cancelled)
        self.task_manager.log_message.connect(self.add_log)

        # 广播服务信号
        self.broadcast_service.broadcast_started.connect(self.on_broadcast_started)
        self.broadcast_service.broadcast_stopped.connect(self.on_broadcast_stopped)
        self.broadcast_service.log_message.connect(self.add_log)

    def setup_timer(self):
        """设置定时器"""
        # 定时更新统计信息
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_statistics)
        self.stats_timer.start(5000)  # 每5秒更新一次

        # 自动读卡定时器
        self.auto_read_timer = QTimer()
        self.auto_read_timer.timeout.connect(self.check_auto_read)
        self.auto_read_timer.start(500)  # 每500ms检查一次

    def add_log(self, message: str):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logger.info(message)

    @pyqtSlot(str, str)
    def on_client_connected(self, client_id: str, address: str):
        """PC端连接"""
        self.add_log(f"PC端连接: {address} (ID: {client_id})")
        self.update_client_table()

    @pyqtSlot(str)
    def on_client_disconnected(self, client_id: str):
        """PC端断开连接"""
        self.add_log(f"PC端断开连接: {client_id}")
        self.update_client_table()

    @pyqtSlot(str, str)
    def on_task_assigned(self, task_id: str, client_id: str):
        """任务分配"""
        self.add_log(f"任务分配: {task_id} -> {client_id}")
        self.update_task_table()

    @pyqtSlot(str, str)
    def on_photo_uploaded(self, task_id: str, client_id: str):
        """照片上传完成"""
        self.add_log(f"照片上传完成: {task_id} (来自: {client_id})")
        self.update_task_table()

    @pyqtSlot(str)
    def on_task_cancelled(self, task_id: str):
        """任务取消"""
        self.add_log(f"任务取消: {task_id}")
        self.update_task_table()

    def connect_reader(self):
        """连接/断开读卡器"""
        try:
            if self.reader_connected:
                # 当前已连接，执行断开操作
                if self.reader.disconnect():
                    self.reader_connected = False
                    self.reader_status_text.setText("状态: 未连接")
                    self.reader_status_text.setStyleSheet("color: red; font-weight: bold;")
                    self.reader_status_label.setText("读卡器: 未连接")
                    self.auto_read_btn.setEnabled(False)
                    self.auto_read_btn.setText("启用自动读卡")
                    self.auto_read_enabled = False
                    self.reader.enable_auto_read(False)
                    self.connect_reader_btn.setText("连接读卡器")
                    self.add_log("读卡器连接已断开")
                else:
                    self.add_log("读卡器断开失败")
                    QMessageBox.warning(self, "断开失败", "无法断开读卡器连接")
            else:
                # 当前未连接，执行连接操作
                if self.reader.connect(config.CARD_READER_PORT):
                    self.reader_connected = True
                    self.reader_status_text.setText("状态: 已连接")
                    self.reader_status_text.setStyleSheet("color: green; font-weight: bold;")
                    self.reader_status_label.setText("读卡器: 已连接")
                    self.auto_read_btn.setEnabled(True)
                    self.connect_reader_btn.setText("断开读卡器")
                    self.add_log("读卡器连接成功")
                else:
                    self.add_log("读卡器连接失败")
                    QMessageBox.warning(self, "连接失败", "无法连接到读卡器，请检查设备连接")
        except Exception as e:
            self.add_log(f"读卡器操作异常: {e}")
            QMessageBox.critical(self, "操作错误", f"读卡器操作异常：\n{str(e)}")

    def toggle_auto_read(self):
        """切换自动读卡状态"""
        if not self.reader_connected:
            QMessageBox.warning(self, "设备未连接", "请先连接读卡器")
            return

        self.auto_read_enabled = not self.auto_read_enabled
        self.reader.enable_auto_read(self.auto_read_enabled)

        if self.auto_read_enabled:
            self.auto_read_btn.setText("禁用自动读卡")
            self.auto_read_btn.setStyleSheet("background-color: #ff6b6b; color: white;")
            self.add_log("自动读卡已启用")

            # 启用自动读卡时自动显示机位号窗口
            if not self.station_display.isVisible():
                self.station_display.show()
                self.station_display.raise_()
                self.station_display.activateWindow()
                self.station_display_btn.setText("隐藏机位号窗口")
                self.station_display_btn.setStyleSheet("background-color: #e74c3c; color: white;")
                self.add_log("机位号显示窗口已自动显示")
        else:
            self.auto_read_btn.setText("启用自动读卡")
            self.auto_read_btn.setStyleSheet("")
            self.add_log("自动读卡已禁用")

            # 禁用自动读卡时隐藏机位号窗口
            if self.station_display.isVisible():
                self.station_display.hide()
                self.station_display_btn.setText("显示机位号窗口")
                self.station_display_btn.setStyleSheet("background-color: #3498db; color: white;")
                self.add_log("机位号显示窗口已自动隐藏")

    def check_auto_read(self):
        """检查自动读卡"""
        if not self.auto_read_enabled or not self.reader_connected:
            return

        try:
            # 检查卡片状态变化
            changed, status = self.reader.check_card_status_changed()

            if changed and status == "card_inserted":
                self.add_log("检测到新卡片，等待稳定后读取...")
                # 延时500ms让卡片稳定
                QTimer.singleShot(500, self.auto_read_card)

        except Exception as e:
            logger.error(f"自动读卡检查异常: {e}")

    def toggle_station_display(self):
        """切换机位号显示窗口"""
        try:
            if self.station_display.isVisible():
                self.station_display.hide()
                self.station_display_btn.setText("显示机位号窗口")
                self.station_display_btn.setStyleSheet("background-color: #3498db; color: white;")
                self.add_log("机位号显示窗口已隐藏")
            else:
                self.station_display.show()
                self.station_display.raise_()
                self.station_display.activateWindow()
                self.station_display_btn.setText("隐藏机位号窗口")
                self.station_display_btn.setStyleSheet("background-color: #e74c3c; color: white;")
                self.add_log("机位号显示窗口已显示")
        except Exception as e:
            self.add_log(f"切换机位号显示窗口失败: {e}")
            logger.error(f"切换机位号显示窗口异常: {e}")

    def on_station_window_closed(self):
        """机位号窗口关闭回调"""
        try:
            # 更新按钮状态
            self.station_display_btn.setText("显示机位号窗口")
            self.station_display_btn.setStyleSheet("background-color: #3498db; color: white;")
            self.add_log("机位号显示窗口已关闭")
        except Exception as e:
            logger.error(f"处理机位号窗口关闭事件异常: {e}")

    def get_assigned_station_for_task(self, task_id: str) -> int:
        """获取任务分配的机位号"""
        try:
            # 查找哪个客户端被分配了这个任务
            clients_info = self.task_manager.get_connected_clients_info()

            # 简单策略：返回第一个连接的客户端的机位号
            # 实际应用中可以根据任务分配算法来确定
            if clients_info:
                first_client = list(clients_info.values())[0]
                return first_client.get("station_number", 1)

            return 1  # 默认返回1号机位

        except Exception as e:
            logger.error(f"获取任务分配机位号失败: {e}")
            return 1

    def get_assigned_station_for_existing_record(self, id_number: str) -> int:
        """获取已存在记录的分配机位号"""
        try:
            # 查找该身份证号对应的任务
            task = self.db.get_task_by_id_number(id_number)
            if task and task.get('pc_client_id'):
                # 查找分配的客户端机位号
                client_id = task['pc_client_id']
                if client_id in self.task_manager.connected_clients:
                    return self.task_manager.connected_clients[client_id].get('station_number', 1)

            # 如果没有找到分配的客户端，返回第一个可用的机位号
            clients_info = self.task_manager.get_connected_clients_info()
            if clients_info:
                first_client = list(clients_info.values())[0]
                return first_client.get("station_number", 1)

            return 1  # 默认返回1号机位

        except Exception as e:
            logger.error(f"获取已存在记录机位号失败: {e}")
            return 1

    def show_station_manager(self):
        """显示机位号管理对话框"""
        try:
            dialog = StationManagerDialog(self.task_manager, self)
            dialog.exec_()

            # 刷新客户端表格
            self.update_client_table()

        except Exception as e:
            self.add_log(f"显示机位号管理对话框失败: {e}")
            logger.error(f"显示机位号管理对话框异常: {e}")

    def auto_read_card(self):
        """自动读取身份证"""
        try:
            card_info, error_msg = self.reader.read_card_info()

            if card_info:
                # 检查是否已存在相同身份证号码
                if self.db.check_id_number_exists(card_info['id_number']):
                    self.add_log(f"身份证号码已存在: {card_info['name']} ({card_info['id_number']})")

                    # 查找已存在记录分配的机位号
                    assigned_station = self.get_assigned_station_for_existing_record(card_info['id_number'])

                    # 在机位号显示窗口显示分配的机位号
                    if assigned_station and self.station_display.isVisible():
                        self.station_display.show_station(assigned_station, card_info['name'])

                    self.add_log(f"重复刷卡 - 姓名: {card_info['name']}, 身份证: {card_info['id_number']}, 分配机位: {assigned_station}号")
                    return

                # 创建任务
                task_id = self.db.create_task(card_info)

                if task_id:
                    self.add_log(f"自动读卡成功: {card_info['name']} ({card_info['id_number']})")
                    self.add_log(f"创建任务: {task_id}")

                    # 更新界面
                    self.update_task_table()
                    self.update_statistics()

                    # 自动分配任务给连接的PC端
                    self.task_manager.check_and_assign_tasks()

                    # 获取分配的机位号
                    assigned_station = self.get_assigned_station_for_task(task_id)

                    # 在机位号显示窗口显示分配的机位号
                    if assigned_station and self.station_display.isVisible():
                        self.station_display.show_station(assigned_station, card_info['name'])

                    # 只在日志中记录，不显示弹出窗口
                    self.add_log(f"读卡成功 - 姓名: {card_info['name']}, 身份证: {card_info['id_number']}, 分配机位: {assigned_station}号")
                else:
                    self.add_log("创建任务失败")
            else:
                # 显示具体的读卡失败原因
                if error_msg:
                    self.add_log(f"自动读卡失败: {error_msg}")
                else:
                    self.add_log("自动读卡失败: 未知原因")

        except Exception as e:
            self.add_log(f"自动读卡异常: {e}")
            logger.error(f"自动读卡异常: {e}")

    def delete_selected_tasks(self):
        """删除选中的任务（支持多选）"""
        selected_rows = set()
        for item in self.task_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "未选择任务", "请先选择要删除的任务")
            return

        # 获取选中任务的信息
        tasks_to_delete = []
        table_name = self.get_current_table_name()

        for row in selected_rows:
            task_id_item = self.task_table.item(row, 0)
            name_item = self.task_table.item(row, 1)
            id_number_item = self.task_table.item(row, 2)

            if all([task_id_item, name_item, id_number_item]):
                task_id_short = task_id_item.text()
                name = name_item.text()

                # 从数据库获取完整任务ID
                tasks = self.db.get_all_tasks(table_name)
                full_task_id = None
                for task in tasks:
                    if task[0].startswith(task_id_short) and task[1] == name:
                        full_task_id = task[0]
                        break

                if full_task_id:
                    tasks_to_delete.append({
                        'row': row,
                        'task_id': full_task_id,
                        'task_id_short': task_id_short,
                        'name': name,
                        'id_number': id_number_item.text()
                    })

        if not tasks_to_delete:
            QMessageBox.warning(self, "获取任务信息失败", "无法获取选中任务的信息")
            return

        # 构建确认消息
        task_count = len(tasks_to_delete)
        if task_count == 1:
            task = tasks_to_delete[0]
            confirm_msg = (f"确定要删除以下任务吗？\n\n"
                          f"姓名: {task['name']}\n"
                          f"身份证号: {task['id_number']}\n"
                          f"任务ID: {task['task_id_short']}...\n\n"
                          f"此操作不可撤销！")
        else:
            confirm_msg = f"确定要删除选中的 {task_count} 个任务吗？\n\n"
            for i, task in enumerate(tasks_to_delete[:3]):  # 最多显示3个
                confirm_msg += f"{i+1}. {task['name']} ({task['id_number']})\n"
            if task_count > 3:
                confirm_msg += f"... 还有 {task_count-3} 个任务\n"
            confirm_msg += "\n此操作不可撤销！"

        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除",
            confirm_msg,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 批量删除任务
                success_count = 0

                for task in tasks_to_delete:
                    delete_result = self.db.delete_task(task['task_id'])
                    if delete_result.get("success", False):
                        success_count += 1
                        self.add_log(f"任务已删除: {task['name']} ({task['task_id_short']})")

                        # 如果任务已分配给PC客户端，通知客户端删除任务
                        pc_client_id = delete_result.get("pc_client_id")
                        if pc_client_id and delete_result.get("status") == "assigned":
                            if self.task_manager.notify_task_deleted(task['task_id'], pc_client_id):
                                self.add_log(f"已通知PC客户端 {pc_client_id} 删除任务: {task['task_id_short']}")
                            else:
                                self.add_log(f"通知PC客户端删除任务失败: {task['task_id_short']}")
                    else:
                        self.add_log(f"删除任务失败: {task['name']} - {delete_result.get('message', '未知错误')}")

                if success_count > 0:
                    self.add_log(f"批量删除任务成功: {success_count}/{task_count} 个任务")
                    self.update_task_table()
                    self.update_statistics()
                    QMessageBox.information(self, "删除成功", f"成功删除 {success_count} 个任务")
                else:
                    self.add_log("批量删除任务失败")
                    QMessageBox.warning(self, "删除失败", "删除任务失败")

            except Exception as e:
                self.add_log(f"批量删除任务异常: {e}")
                QMessageBox.critical(self, "删除错误", f"删除任务时发生错误：\n{str(e)}")

    def delete_all_pending_tasks(self):
        """删除所有未完成的任务"""
        # 获取当前选定的表名
        table_name = self.get_current_table_name()
        table_display_name = "测试任务" if table_name == "test_tasks" else "正式任务"

        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除当前表({table_display_name})中所有未完成的任务吗？\n\n"
            "这将删除所有状态为'pending'和'assigned'的任务。\n\n"
            "此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                deleted_count = self.db.delete_all_pending_tasks(table_name)

                if deleted_count > 0:
                    self.add_log(f"删除所有未完成任务成功: {deleted_count} 个任务 (表: {table_display_name})")
                    self.update_task_table()
                    self.update_statistics()
                    QMessageBox.information(self, "删除成功", f"成功删除 {deleted_count} 个未完成任务")
                else:
                    self.add_log(f"没有未完成的任务需要删除 (表: {table_display_name})")
                    QMessageBox.information(self, "删除完成", "没有未完成的任务需要删除")

            except Exception as e:
                self.add_log(f"删除所有未完成任务异常: {e}")
                QMessageBox.critical(self, "删除错误", f"删除任务时发生错误：\n{str(e)}")

    def clear_all_data(self):
        """清空所有数据"""
        # 获取当前选定的表名
        table_name = self.get_current_table_name()
        table_display_name = "测试任务" if table_name == "test_tasks" else "正式任务"

        # 确认清空
        reply = QMessageBox.question(
            self, "确认清空",
            f"确定要清空当前表({table_display_name})中的所有数据吗？\n\n"
            "这将删除所有任务记录，包括已完成和未完成的任务。\n\n"
            "此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                deleted_count = self.db.clear_all_tasks(table_name)

                if deleted_count > 0:
                    self.add_log(f"清空所有数据成功: {deleted_count} 个任务 (表: {table_display_name})")
                    self.update_task_table()
                    self.update_statistics()
                    QMessageBox.information(self, "清空成功", f"成功清空 {deleted_count} 个任务")
                else:
                    self.add_log(f"没有数据需要清空 (表: {table_display_name})")
                    QMessageBox.information(self, "清空完成", "没有数据需要清空")

            except Exception as e:
                self.add_log(f"清空所有数据异常: {e}")
                QMessageBox.critical(self, "清空错误", f"清空数据时发生错误：\n{str(e)}")

    def clear_station_assignments(self):
        """清空机位号分配"""
        # 确认清空
        reply = QMessageBox.question(
            self, "确认清空机位号分配",
            "确定要清空所有机位号分配记录吗？\n\n"
            "这将删除所有已分配的机位号，下次连接时将重新从1号开始分配。\n\n"
            "此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 清空任务管理器中的机位号分配
                if hasattr(self, 'task_manager') and self.task_manager:
                    cleared_count = self.task_manager.clear_station_assignments()

                    self.add_log(f"清空机位号分配成功: {cleared_count} 个分配记录")
                    QMessageBox.information(self, "清空成功", f"成功清空 {cleared_count} 个机位号分配记录\n下次连接将重新从1号开始分配")
                else:
                    QMessageBox.warning(self, "清空失败", "任务管理器未初始化")

            except Exception as e:
                self.add_log(f"清空机位号分配异常: {e}")
                QMessageBox.critical(self, "清空错误", f"清空机位号分配时发生错误：\n{str(e)}")

    def open_photo_settings(self):
        """打开照片参数设置对话框"""
        try:
            from .photo_settings_dialog import PhotoSettingsDialog

            # 获取当前照片参数
            current_settings = self.task_manager.photo_settings

            dialog = PhotoSettingsDialog(current_settings, self)
            if dialog.exec_() == QDialog.Accepted:
                new_settings = dialog.get_settings()

                # 更新服务端照片参数
                if self.task_manager.update_photo_settings(new_settings):
                    self.add_log("照片参数设置已更新")
                    QMessageBox.information(self, "设置成功", "照片参数设置已更新")
                else:
                    self.add_log("照片参数设置更新失败")
                    QMessageBox.warning(self, "设置失败", "照片参数设置更新失败")

        except ImportError:
            QMessageBox.warning(self, "功能不可用", "照片参数设置对话框不可用")
        except Exception as e:
            self.add_log(f"打开照片参数设置异常: {e}")
            QMessageBox.critical(self, "设置错误", f"打开照片参数设置时发生错误：\n{str(e)}")


    def start_server(self):
        """启动服务器"""
        if self.server_running:
            return

        try:
            # 在新线程中启动服务器
            server_thread = threading.Thread(target=self.task_manager.start_server)
            server_thread.daemon = True
            server_thread.start()

            # 启动广播服务
            self.broadcast_service.start_broadcast()

            self.server_running = True
            self.server_status_text.setText("状态: 运行中")
            self.server_status_text.setStyleSheet("color: green; font-weight: bold;")
            self.server_status_label.setText(f"服务器: 运行中 (端口: {config.SERVER_PORT})")
            self.start_server_btn.setEnabled(False)
            self.stop_server_btn.setEnabled(True)

            self.add_log(f"服务器启动成功，监听端口: {config.SERVER_PORT}")

            # 检查数据库中的未完成任务并加入等待列表
            self.check_and_load_pending_tasks()

        except Exception as e:
            self.add_log(f"服务器启动失败: {e}")
            QMessageBox.critical(self, "启动失败", f"服务器启动失败：\n{str(e)}")

    def check_and_load_pending_tasks(self):
        """检查数据库中的未完成任务并加入等待列表"""
        try:
            # 首先重置所有无照片记录的任务状态
            reset_count = self.db.reset_incomplete_tasks_status()
            if reset_count > 0:
                self.add_log(f"重置了 {reset_count} 个无照片记录的任务状态为未完成")

            # 获取所有未分配的任务
            unassigned_tasks = self.db.get_unassigned_tasks()
            if unassigned_tasks:
                self.add_log(f"发现 {len(unassigned_tasks)} 个未完成任务，已加入等待队列")
                # 如果有PC端连接，立即分配任务
                self.task_manager.check_and_assign_tasks()
            else:
                self.add_log("没有发现未完成任务")

            # 更新统计信息
            self.update_statistics()
            self.update_task_table()

        except Exception as e:
            logger.error(f"检查未完成任务失败: {e}")
            self.add_log(f"检查未完成任务失败: {e}")

    def stop_server(self):
        """停止服务器"""
        if not self.server_running:
            return

        try:
            self.task_manager.stop_server()

            # 停止广播服务
            self.broadcast_service.stop_broadcast()

            # 关闭机位号窗口
            if self.station_display and self.station_display.isVisible():
                self.station_display.close()
                self.add_log("机位号显示窗口已关闭")

            self.server_running = False
            self.server_status_text.setText("状态: 已停止")
            self.server_status_text.setStyleSheet("color: red; font-weight: bold;")
            self.server_status_label.setText("服务器: 已停止")
            self.start_server_btn.setEnabled(True)
            self.stop_server_btn.setEnabled(False)

            self.add_log("服务器已停止")

        except Exception as e:
            self.add_log(f"停止服务器失败: {e}")
            QMessageBox.critical(self, "停止失败", f"停止服务器失败：\n{str(e)}")

    def update_statistics(self):
        """更新统计信息"""
        try:
            # 获取当前选中的表名
            table_name = self.get_current_table_name()
            # 定时器调用时只在有变化时记录日志
            stats = self.db.get_task_statistics(table_name, log_changes_only=True)

            self.total_tasks_label.setText(f"总任务: {stats['total']}")
            self.pending_tasks_label.setText(f"待处理: {stats['pending']}")
            self.assigned_tasks_label.setText(f"已分配: {stats['assigned']}")
            self.completed_tasks_label.setText(f"已完成: {stats['completed']}")

            # 如果有暂停任务，显示暂停任务数量
            suspended_count = stats.get('suspended', 0)
            if suspended_count > 0:
                self.task_stats_label.setText(
                    f"任务: {stats['pending']}/{stats['assigned']}/{stats['completed']} (暂停: {suspended_count})"
                )
            else:
                self.task_stats_label.setText(
                    f"任务: {stats['pending']}/{stats['assigned']}/{stats['completed']}"
                )

        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")

    def update_task_table(self):
        """更新任务表格"""
        try:
            # 获取当前选中的表名
            table_name = self.get_current_table_name()
            tasks = self.db.get_all_tasks(table_name)

            self.task_table.setRowCount(len(tasks))

            for row, task in enumerate(tasks):
                task_id, name, gender, birth_date, id_number, task_status, pc_client_id, created_time, assigned_time, completed_time = task

                self.task_table.setItem(row, 0, QTableWidgetItem(task_id[:8]))  # 显示前8位
                self.task_table.setItem(row, 1, QTableWidgetItem(name))
                self.task_table.setItem(row, 2, QTableWidgetItem(id_number))
                self.task_table.setItem(row, 3, QTableWidgetItem(task_status))
                self.task_table.setItem(row, 4, QTableWidgetItem(created_time))
                self.task_table.setItem(row, 5, QTableWidgetItem(pc_client_id or ""))

        except Exception as e:
            logger.error(f"更新任务表格失败: {e}")

    def reassign_selected_tasks(self):
        """重新分配选中的任务"""
        try:
            selected_rows = set()
            for item in self.task_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.warning(self, "提示", "请先选择要重新分配的任务")
                return

            # 获取选中任务的信息
            selected_tasks = []
            table_name = self.get_current_table_name()

            for row in selected_rows:
                task_id_item = self.task_table.item(row, 0)
                name_item = self.task_table.item(row, 1)
                status_item = self.task_table.item(row, 3)

                if task_id_item and name_item and status_item:
                    # 获取完整的任务ID（显示的是前8位）
                    task_id_short = task_id_item.text()
                    name = name_item.text()
                    status = status_item.text()

                    # 从数据库获取完整任务ID
                    tasks = self.db.get_all_tasks(table_name)
                    full_task_id = None
                    for task in tasks:
                        if task[0].startswith(task_id_short) and task[1] == name:
                            full_task_id = task[0]
                            break

                    if full_task_id:
                        selected_tasks.append({
                            'task_id': full_task_id,
                            'name': name,
                            'status': status
                        })

            if not selected_tasks:
                QMessageBox.warning(self, "错误", "无法获取选中任务的信息")
                return

            # 确认对话框
            task_list = "\n".join([f"- {task['name']} ({task['status']})" for task in selected_tasks])
            reply = QMessageBox.question(
                self,
                "确认重新分配",
                f"确定要重新分配以下 {len(selected_tasks)} 个任务吗？\n\n{task_list}\n\n任务将被重新设置为待分配状态，并自动分配给可用的PC客户端。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 执行重新分配
            success_count = 0
            for task in selected_tasks:
                if self.db.reassign_task_to_pending(task['task_id']):
                    success_count += 1
                    self.add_log(f"任务重新分配: {task['name']} ({task['task_id'][:8]})")

            if success_count > 0:
                # 触发任务重新分配
                self.task_manager.check_and_assign_tasks()

                # 更新界面
                self.update_task_table()
                self.update_statistics()

                QMessageBox.information(
                    self,
                    "重新分配完成",
                    f"成功重新分配了 {success_count} 个任务"
                )
                self.add_log(f"手动重新分配了 {success_count} 个任务")
            else:
                QMessageBox.warning(self, "重新分配失败", "没有任务被成功重新分配")

        except Exception as e:
            logger.error(f"重新分配任务失败: {e}")
            QMessageBox.critical(self, "错误", f"重新分配任务失败：\n{str(e)}")

    def show_task_context_menu(self, position):
        """显示任务表格右键菜单"""
        try:
            # 检查是否点击在有效行上
            item = self.task_table.itemAt(position)
            if item is None:
                return

            # 创建右键菜单
            context_menu = QMenu(self)

            # 重新分配任务
            reassign_action = QAction("重新分配任务", self)
            reassign_action.triggered.connect(self.reassign_selected_tasks)
            context_menu.addAction(reassign_action)

            # 删除任务
            delete_action = QAction("删除任务", self)
            delete_action.triggered.connect(self.delete_selected_tasks)
            context_menu.addAction(delete_action)

            context_menu.addSeparator()

            # 查看任务详情
            detail_action = QAction("查看任务详情", self)
            detail_action.triggered.connect(self.show_task_detail)
            context_menu.addAction(detail_action)

            # 显示菜单
            context_menu.exec_(self.task_table.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示右键菜单失败: {e}")

    def show_task_detail(self):
        """显示任务详情"""
        try:
            selected_rows = set()
            for item in self.task_table.selectedItems():
                selected_rows.add(item.row())

            if len(selected_rows) != 1:
                QMessageBox.warning(self, "提示", "请选择一个任务查看详情")
                return

            row = list(selected_rows)[0]
            task_id_item = self.task_table.item(row, 0)
            name_item = self.task_table.item(row, 1)

            if not task_id_item or not name_item:
                QMessageBox.warning(self, "错误", "无法获取任务信息")
                return

            task_id_short = task_id_item.text()
            name = name_item.text()

            # 从数据库获取完整任务信息
            tasks = self.db.get_all_tasks()
            task_detail = None
            for task in tasks:
                if task[0].startswith(task_id_short) and task[1] == name:
                    task_detail = task
                    break

            if not task_detail:
                QMessageBox.warning(self, "错误", "无法找到任务详情")
                return

            # 显示任务详情对话框
            detail_text = f"""任务详情：

任务ID: {task_detail[0]}
姓名: {task_detail[1]}
性别: {task_detail[2]}
出生日期: {task_detail[3]}
身份证号: {task_detail[4]}
任务状态: {task_detail[5]}
分配的PC端: {task_detail[6] or '未分配'}
创建时间: {task_detail[7]}
分配时间: {task_detail[8] or '未分配'}
完成时间: {task_detail[9] or '未完成'}"""

            QMessageBox.information(self, "任务详情", detail_text)

        except Exception as e:
            logger.error(f"显示任务详情失败: {e}")
            QMessageBox.critical(self, "错误", f"显示任务详情失败：\n{str(e)}")

    def update_client_table(self):
        """更新客户端表格"""
        try:
            clients_info = self.task_manager.get_connected_clients_info()
            self.add_log(f"更新客户端表格，当前连接数: {len(clients_info)}")

            self.client_table.setRowCount(len(clients_info))

            for row, (client_id, info) in enumerate(clients_info.items()):
                self.client_table.setItem(row, 0, QTableWidgetItem(client_id))
                self.client_table.setItem(row, 1, QTableWidgetItem(info.get("address", "未知")))
                self.client_table.setItem(row, 2, QTableWidgetItem(f"{info.get('station_number', 1)}号"))
                self.client_table.setItem(row, 3, QTableWidgetItem(info.get("connected_time", "未知")))
                # 显示未完成任务数量/总任务数量
                incomplete_tasks = info.get("incomplete_tasks", 0)
                total_tasks = info.get("tasks", 0)
                task_info = f"{incomplete_tasks}/{total_tasks}"
                self.client_table.setItem(row, 4, QTableWidgetItem(task_info))

        except Exception as e:
            logger.error(f"更新客户端表格失败: {e}")
            self.add_log(f"更新客户端表格失败: {e}")

    def show_export_dialog(self):
        """显示导出对话框"""
        dialog = ExportDialog(self.exporter, self)
        dialog.exec_()

    def export_task_list(self):
        """导出任务列表"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出任务列表", config.DEFAULT_CSV_FILE, "CSV文件 (*.csv)"
            )

            if file_path:
                count = self.exporter.export_task_list(file_path)
                self.add_log(f"任务列表导出完成: {file_path} ({count} 条记录)")
                QMessageBox.information(
                    self, "导出完成",
                    f"任务列表导出完成\n文件: {file_path}\n记录数: {count}"
                )

        except Exception as e:
            self.add_log(f"导出任务列表失败: {e}")
            QMessageBox.critical(self, "导出失败", f"导出任务列表失败：\n{str(e)}")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, "关于",
            f"{config.APP_NAME} v{config.APP_VERSION}\n\n"
            "身份证读卡器服务端程序\n"
            "支持身份证信息读取、任务管理和照片导出\n\n"
            "开发：AI助手"
        )

    def closeEvent(self, event):
        """关闭事件"""
        if self.server_running:
            reply = QMessageBox.question(
                self, "确认退出",
                "服务器正在运行，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.stop_server()
                # 停止广播服务
                self.broadcast_service.stop_broadcast()
                # 关闭桥接进程
                self.reader.close_bridge()
                # 关闭机位号窗口
                if self.station_display and self.station_display.isVisible():
                    self.station_display.close()
                event.accept()
            else:
                event.ignore()
        else:
            # 停止广播服务
            self.broadcast_service.stop_broadcast()
            # 关闭桥接进程
            self.reader.close_bridge()
            # 关闭机位号窗口
            if self.station_display and self.station_display.isVisible():
                self.station_display.close()
            event.accept()

    def on_broadcast_started(self):
        """广播服务启动回调"""
        self.add_log("广播服务已启动，PC端可以自动发现服务端")

    def on_broadcast_stopped(self):
        """广播服务停止回调"""
        self.add_log("广播服务已停止")

    def generate_test_tasks(self):
        """生成测试任务"""
        # 询问生成数量
        count, ok = QInputDialog.getInt(
            self, "生成测试任务",
            "请输入要生成的测试任务数量:",
            10, 1, 1000, 1
        )

        if ok:
            try:
                success = self.db.generate_test_tasks(count)
                if success:
                    self.add_log(f"成功生成 {count} 个测试任务")
                    # 如果当前显示的是测试任务表，刷新显示
                    if self.test_table_btn.isChecked():
                        self.update_task_table()
                    self.update_statistics()
                    QMessageBox.information(self, "成功", f"成功生成 {count} 个测试任务")
                else:
                    QMessageBox.warning(self, "失败", "生成测试任务失败")
            except Exception as e:
                self.add_log(f"生成测试任务异常: {e}")
                QMessageBox.critical(self, "错误", f"生成测试任务异常: {e}")

    def on_table_selection_changed(self):
        """表选择变化处理"""
        self.update_task_table()
        self.update_statistics()

    def get_current_table_name(self):
        """获取当前选中的表名"""
        if self.test_table_btn.isChecked():
            return "test_tasks"
        else:
            return "photo_tasks"

    def get_current_task_source(self):
        """获取当前选中的任务来源表名"""
        if self.test_source_btn.isChecked():
            return "test_tasks"
        else:
            return "photo_tasks"

    def on_task_source_changed(self):
        """任务来源变化处理"""
        # 更新任务管理器的任务来源
        task_source = self.get_current_task_source()
        self.task_manager.set_task_source(task_source)
        self.add_log(f"任务来源已切换到: {'测试任务' if task_source == 'test_tasks' else '正式任务'}")
