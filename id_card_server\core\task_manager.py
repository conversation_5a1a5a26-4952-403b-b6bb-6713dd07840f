#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理器
"""

import socket
import threading
import json
import base64
import uuid
import logging
import time
import os
from datetime import datetime
from typing import Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal
import config

logger = logging.getLogger(__name__)

class TaskManager(QObject):
    """任务管理器"""
    
    # 信号定义
    client_connected = pyqtSignal(str, str)  # client_id, address
    client_disconnected = pyqtSignal(str)    # client_id
    task_assigned = pyqtSignal(str, str)     # task_id, client_id
    photo_uploaded = pyqtSignal(str, str)    # task_id, client_id
    task_cancelled = pyqtSignal(str)         # task_id
    log_message = pyqtSignal(str)            # log message
    
    def __init__(self, database_manager, port=9090):
        super().__init__()
        self.db = database_manager
        self.port = port
        self.server_socket = None
        self.running = False
        self.connected_clients = {}  # 存储连接的PC端信息
        self.photo_settings = config.DEFAULT_PHOTO_SETTINGS.copy()  # 照片参数设置
        self.client_stations = {}  # 存储客户端机位号分配 {client_id: station_number}
        self.machine_stations = {}  # 存储机器码到机位号的映射 {machine_id: station_number}
        self.next_station_number = 1  # 下一个可分配的机位号
        self.task_source = "photo_tasks"  # 任务来源表名，默认为正式任务

        # 加载保存的照片设置
        self.load_photo_settings()

        # 加载机位号分配记录
        self.load_station_assignments()

        # 定时任务分配检查
        self.assignment_timer_running = False

    def set_task_source(self, table_name: str):
        """设置任务来源表"""
        if table_name in ["photo_tasks", "test_tasks"]:
            self.task_source = table_name
            logger.info(f"任务来源已设置为: {table_name}")

            # 通知所有连接的PC客户端切换模式
            self.broadcast_test_mode_to_clients(table_name == "test_tasks")
        else:
            logger.warning(f"无效的任务来源表名: {table_name}")

    def start_server(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('0.0.0.0', self.port))
            self.server_socket.listen(5)
            self.running = True
            
            self.log_message.emit(f"任务服务器启动，监听端口: {self.port}")
            logger.info(f"任务服务器启动，监听端口: {self.port}")

            # 启动定时任务分配检查
            self.start_assignment_timer()
            logger.info("定时任务分配检查已启动（每1秒检查一次）")

            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    
                    # 为每个客户端生成临时ID（等待获取机器码后更新）
                    temp_client_id = str(uuid.uuid4())[:8]

                    address_str = f"{address[0]}:{address[1]}"
                    self.log_message.emit(f"PC客户端连接: {address_str} (临时ID: {temp_client_id})")
                    logger.info(f"新客户端连接: {address_str} (临时ID: {temp_client_id})")
                    self.client_connected.emit(temp_client_id, address_str)
                    
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address, temp_client_id)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        logger.error(f"接受连接时出错: {e}")
                        self.log_message.emit(f"接受连接时出错: {e}")
                        
        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
            self.log_message.emit(f"启动服务器失败: {e}")
    
    def start_assignment_timer(self):
        """启动定时任务分配检查"""
        self.assignment_timer_running = True
        self._schedule_next_assignment_check()

    def _schedule_next_assignment_check(self):
        """安排下一次任务分配检查"""
        if self.assignment_timer_running:
            timer = threading.Timer(1.0, self._assignment_timer_callback)
            timer.daemon = True
            timer.start()

    def _assignment_timer_callback(self):
        """定时器回调函数"""
        try:
            if self.assignment_timer_running:
                self.check_and_assign_tasks()
                self._schedule_next_assignment_check()
        except Exception as e:
            logger.error(f"定时任务分配检查异常: {e}")
            if self.assignment_timer_running:
                self._schedule_next_assignment_check()

    def stop_server(self):
        """停止服务器"""
        self.running = False

        # 停止定时任务分配检查
        self.assignment_timer_running = False
        logger.info("定时任务分配检查已停止")

        # 关闭所有客户端连接
        for client_id, info in list(self.connected_clients.items()):
            try:
                info["socket"].close()
            except:
                pass
        
        self.connected_clients.clear()
        
        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        self.log_message.emit("任务服务器已停止")
        logger.info("任务服务器已停止")
    
    def handle_client(self, client_socket, address, client_id):
        """处理客户端连接"""
        try:
            # 存储客户端连接信息（先存储基本信息）
            current_time = time.time()
            self.connected_clients[client_id] = {
                "socket": client_socket,
                "address": address,
                "connected_time": current_time,
                "last_heartbeat": current_time,
                "machine_id": None,
                "machine_info": None,
                "station_number": 1  # 临时分配，等待连接消息更新
            }

            address_str = f"{address[0]}:{address[1]}"
            self.log_message.emit(f"客户端连接: {address_str} (ID: {client_id})")
            self.client_connected.emit(client_id, address_str)

            while self.running:
                # 接收消息，支持大消息
                data = self._receive_large_message(client_socket)
                if not data:
                    break

                try:
                    message = json.loads(data)
                    msg_type = message.get('type', 'unknown')
                    logger.info(f"收到客户端 {client_id} 消息: {msg_type}")

                    # 如果是connect消息，检查是否需要更新客户端ID
                    if msg_type == 'connect':
                        client_info = message.get('client_info', {})
                        machine_id = client_info.get('machine_id', '')

                        if machine_id:
                            # 使用机器码生成新的客户端ID
                            new_client_id = f"pc_{machine_id}"

                            if new_client_id != client_id:
                                # 更新客户端ID
                                if client_id in self.connected_clients:
                                    self.connected_clients[new_client_id] = self.connected_clients[client_id]
                                    del self.connected_clients[client_id]

                                logger.info(f"客户端ID已更新: {client_id} -> {new_client_id}")
                                client_id = new_client_id

                    response = self.process_message(message, client_id)

                    # 发送响应
                    try:
                        response_str = json.dumps(response)
                        client_socket.send(response_str.encode('utf-8'))
                        logger.info(f"已向客户端 {client_id} 发送响应: {response.get('status', 'unknown')}")
                    except Exception as send_error:
                        logger.error(f"向客户端 {client_id} 发送响应失败: {send_error}")
                        break  # 发送失败，断开连接

                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}, 数据长度: {len(data) if data else 0}")
                    try:
                        error_response = {"status": "error", "message": f"Invalid JSON: {str(e)}"}
                        client_socket.send(json.dumps(error_response).encode('utf-8'))
                    except:
                        logger.error(f"发送错误响应失败")
                        break
                except Exception as process_error:
                    logger.error(f"处理消息异常: {process_error}")
                    try:
                        error_response = {"status": "error", "message": f"Server error: {str(process_error)}"}
                        client_socket.send(json.dumps(error_response).encode('utf-8'))
                    except:
                        logger.error(f"发送错误响应失败")
                        break
                    
        except ConnectionResetError as e:
            logger.info(f"客户端 {address} 网络连接重置: {e}")
            disconnect_type = "network"
        except ConnectionAbortedError as e:
            logger.info(f"客户端 {address} 连接被中止: {e}")
            disconnect_type = "network"
        except Exception as e:
            logger.error(f"处理客户端 {address} 时出错: {e}")
            disconnect_type = "network"
        finally:
            # 检查是否收到了主动断开消息
            client_info = self.connected_clients.get(client_id, {})
            if client_info.get('graceful_disconnect', False):
                disconnect_type = "graceful"
            else:
                disconnect_type = getattr(self, '_disconnect_type', "network")

            # 处理客户端断开连接
            self._handle_client_disconnect(client_id, address, disconnect_type)

            try:
                client_socket.close()
            except:
                pass

    def _handle_client_disconnect(self, client_id: str, address: tuple, disconnect_type: str = "network"):
        """处理客户端断开连接

        Args:
            client_id: 客户端ID
            address: 客户端地址
            disconnect_type: 断开类型 ("graceful"=主动断开, "network"=网络异常)
        """
        try:
            # 获取客户端信息
            client_info = self.connected_clients.get(client_id)
            station_number = client_info.get("station_number", 0) if client_info else 0

            # 根据断开类型处理任务
            if disconnect_type == "graceful":
                # 主动断开：取消并删除未完成任务
                deleted_count = self.db.cancel_and_delete_client_tasks(client_id)
                if deleted_count > 0:
                    logger.info(f"客户端 {client_id} (机位{station_number}号) 主动断开连接，删除了 {deleted_count} 个未完成任务")
                    self.log_message.emit(f"PC端主动断开连接，删除了 {deleted_count} 个未完成任务")

                    # 重新分配任务给其他PC端
                    try:
                        self.check_and_assign_tasks()
                    except Exception as e:
                        logger.error(f"重新分配任务异常: {e}")
            else:
                # 网络异常断开：暂停任务，等待重连
                suspended_count = self.db.suspend_client_tasks_for_reconnect(client_id)
                if suspended_count > 0:
                    logger.info(f"客户端 {client_id} (机位{station_number}号) 网络异常断开，暂停了 {suspended_count} 个任务，等待重连")
                    self.log_message.emit(f"PC端网络异常断开，暂停了 {suspended_count} 个任务，等待重连")

            # 清理客户端连接信息（但保留机位号分配）
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]

            # 注意：不清理机位号分配，保留给重连时使用
            # if client_id in self.client_stations:
            #     del self.client_stations[client_id]

            address_str = f"{address[0]}:{address[1]}"
            disconnect_reason = "主动断开" if disconnect_type == "graceful" else "网络异常断开"
            self.log_message.emit(f"客户端{disconnect_reason}: {address_str} (ID: {client_id}, 机位{station_number}号)")
            self.client_disconnected.emit(client_id)

        except Exception as e:
            logger.error(f"处理客户端断开连接异常: {e}")

    def _receive_large_message(self, client_socket):
        """接收大消息（支持照片上传等大数据）"""
        try:
            # 先接收一小块数据
            try:
                data = client_socket.recv(8192).decode('utf-8')
                if not data:
                    logger.info("客户端关闭连接")
                    return None

                logger.debug(f"接收到数据，长度: {len(data)}")
            except Exception as recv_error:
                logger.error(f"接收数据失败: {recv_error}")
                return None

            # 如果数据看起来被截断了（通常以不完整的JSON结尾），继续接收
            while data and not self._is_complete_json(data):
                try:
                    client_socket.settimeout(1.0)  # 设置短超时
                    more_data = client_socket.recv(8192).decode('utf-8')
                    if more_data:
                        data += more_data
                        logger.debug(f"继续接收数据，总长度: {len(data)}")
                    else:
                        break
                except socket.timeout:
                    # 超时说明数据接收完毕
                    logger.debug("接收超时，数据接收完毕")
                    break
                except Exception as more_error:
                    logger.error(f"继续接收数据失败: {more_error}")
                    break
                finally:
                    try:
                        client_socket.settimeout(None)  # 恢复阻塞模式
                    except:
                        pass

            logger.debug(f"消息接收完成，总长度: {len(data) if data else 0}")
            return data

        except Exception as e:
            logger.error(f"接收大消息失败: {e}")
            import traceback
            logger.error(f"接收异常详情: {traceback.format_exc()}")
            return None

    def _is_complete_json(self, data):
        """检查是否是完整的JSON"""
        try:
            json.loads(data)
            return True
        except json.JSONDecodeError:
            return False

    def process_message(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理消息"""
        msg_type = message.get('type')
        
        try:
            if msg_type == 'connect':
                return self.handle_client_connect(message, client_id)
            elif msg_type == 'get_photo_settings':
                return self.get_photo_settings()
            elif msg_type == 'get_task':
                return self.get_pending_task(client_id)
            elif msg_type == 'upload_photo':
                return self.handle_photo_upload(message, client_id)
            elif msg_type == 'test_task_complete':
                return self.handle_test_task_complete(message, client_id)
            elif msg_type == 'cancel_task':
                return self.cancel_task(message)
            elif msg_type == 'get_client_info':
                return self.get_client_info(client_id)
            elif msg_type == 'heartbeat':
                return self.handle_heartbeat(message, client_id)
            elif msg_type == 'ready_for_tasks':
                return self.handle_ready_for_tasks(message, client_id)
            elif msg_type == 'client_task_count':
                return self.handle_client_task_count(message, client_id)
            elif msg_type == 'disconnect':
                return self.handle_graceful_disconnect(message, client_id)
            else:
                return {"status": "error", "message": "Unknown message type"}
                
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
            return {"status": "error", "message": f"Server error: {str(e)}"}

    def handle_graceful_disconnect(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理客户端主动断开连接"""
        try:
            # 标记为主动断开
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['graceful_disconnect'] = True

            logger.info(f"客户端 {client_id} 发送主动断开消息")

            return {
                "status": "success",
                "message": "Disconnect acknowledged"
            }

        except Exception as e:
            logger.error(f"处理主动断开消息异常: {e}")
            return {"status": "error", "message": f"Disconnect error: {str(e)}"}

    def handle_client_connect(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理客户端连接"""
        try:
            client_info = message.get('client_info', {})
            machine_id = client_info.get('machine_id', '')
            machine_info = client_info.get('machine_info', {})

            # 默认机位号
            station_number = 1

            # 更新客户端信息
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['info'] = client_info
                self.connected_clients[client_id]['machine_id'] = machine_id
                self.connected_clients[client_id]['machine_info'] = machine_info

                # 根据机器码分配机位号
                station_number = self.assign_station_by_machine_id(machine_id, client_id)
                self.connected_clients[client_id]['station_number'] = station_number

                # 更新日志显示
                address_str = f"{self.connected_clients[client_id]['address'][0]}:{self.connected_clients[client_id]['address'][1]}"
                logger.info(f"客户端连接完成: {address_str} (ID: {client_id}, 机器码: {machine_id}, 机位: {station_number}号)")

                # 检查是否有该客户端的暂停任务需要恢复
                restored_count = self.db.restore_suspended_tasks_for_client(client_id)
                if restored_count > 0:
                    logger.info(f"客户端 {client_id} 重连，恢复了 {restored_count} 个暂停任务")
                    self.log_message.emit(f"客户端重连，恢复了 {restored_count} 个暂停任务")

                # 获取该客户端的所有未完成任务（包括刚恢复的和已分配的）
                incomplete_tasks = self.db.get_pc_client_incomplete_tasks(client_id)
                if incomplete_tasks:
                    logger.info(f"客户端 {client_id} 有 {len(incomplete_tasks)} 个未完成任务，等待客户端准备就绪后发送")
                    self.log_message.emit(f"重连客户端有 {len(incomplete_tasks)} 个未完成任务，等待准备就绪")
                    # 暂存未完成任务，等待客户端发送ready_for_tasks消息
                    self.connected_clients[client_id]['pending_incomplete_tasks'] = incomplete_tasks
                else:
                    self.connected_clients[client_id]['pending_incomplete_tasks'] = []

                # 标记客户端尚未准备接收任务
                self.connected_clients[client_id]['ready_for_tasks'] = False
                logger.info(f"客户端 {client_id} 连接完成，等待准备接收任务的信号")

                # 标记需要发送测试模式状态（在ready_for_tasks时发送）
                self.connected_clients[client_id]['need_test_mode_update'] = True
            else:
                logger.warning(f"客户端 {client_id} 不在连接列表中，使用默认机位号")

            # 返回连接成功和照片参数
            return {
                "status": "success",
                "message": "Connected to server",
                "photo_settings": self.photo_settings,
                "server_mode": True,
                "station_number": station_number
            }
        except Exception as e:
            logger.error(f"处理客户端连接异常: {e}")
            import traceback
            logger.error(f"连接异常详情: {traceback.format_exc()}")
            return {"status": "error", "message": f"Connect failed: {str(e)}"}

    def handle_heartbeat(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理心跳包"""
        try:
            # 更新客户端最后活跃时间
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['last_heartbeat'] = time.time()
                logger.debug(f"收到客户端 {client_id} 心跳包")

            return {
                "status": "success",
                "message": "heartbeat_ok",
                "server_time": time.time()
            }
        except Exception as e:
            logger.error(f"处理心跳包异常: {e}")
            return {"status": "error", "message": f"Heartbeat failed: {str(e)}"}

    def handle_ready_for_tasks(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理客户端准备接收任务的消息"""
        try:
            logger.info(f"收到客户端 {client_id} 的ready_for_tasks消息")
            logger.info(f"客户端 {client_id} 已准备接收任务")

            # 标记客户端已准备接收任务
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['ready_for_tasks'] = True

                # 获取暂存的未完成任务数量（用于响应）
                pending_tasks = self.connected_clients[client_id].get('pending_incomplete_tasks', [])
                pending_count = len(pending_tasks)

                # 先返回响应，然后异步发送任务
                response = {
                    "status": "success",
                    "message": "Ready for tasks acknowledged",
                    "tasks_sent": pending_count
                }

                # 发送该客户端的未完成任务（仅在ready_for_tasks时发送一次）
                try:
                    # 获取客户端的未完成任务
                    incomplete_tasks = self.db.get_pc_client_incomplete_tasks(client_id)

                    if incomplete_tasks:
                        logger.info(f"向重新连接的客户端 {client_id} 发送 {len(incomplete_tasks)} 个未完成任务")
                        for task in incomplete_tasks:
                            if self._send_task_to_client(client_id, task):
                                self.task_assigned.emit(task["task_id"], client_id)
                                logger.info(f"发送未完成任务: {task['task_id']} -> {client_id}")

                    # 清空暂存任务
                    self.connected_clients[client_id]['pending_incomplete_tasks'] = []

                    # 检查是否需要发送测试模式状态
                    if self.connected_clients[client_id].get('need_test_mode_update', False):
                        self.send_test_mode_to_client(client_id, self.task_source == "test_tasks")
                        self.connected_clients[client_id]['need_test_mode_update'] = False

                    logger.info(f"客户端 {client_id} 准备就绪，等待定时器分配新任务")

                except Exception as e:
                    logger.error(f"处理准备就绪状态异常: {e}")

                return response
            else:
                logger.warning(f"客户端 {client_id} 不在连接列表中")
                return {"status": "error", "message": "Client not found"}

        except Exception as e:
            logger.error(f"处理准备接收任务消息异常: {e}")
            return {"status": "error", "message": f"Ready for tasks failed: {str(e)}"}

    def handle_client_task_count(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理客户端任务数量报告"""
        try:
            client_task_count = message.get('task_count', 0)

            # 更新客户端信息
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['client_reported_task_count'] = client_task_count

                # 获取服务端记录的任务数量
                server_task_status = self.get_client_task_status(client_id)
                server_task_count = server_task_status['total_tasks']

                logger.info(f"客户端 {client_id} 报告任务数量: {client_task_count}, 服务端记录: {server_task_count}")

                # 如果数量不一致，可能需要同步
                if abs(client_task_count - server_task_count) > 0:
                    logger.warning(f"客户端 {client_id} 任务数量不一致，可能需要同步")
                    # 可以在这里触发任务同步逻辑

                return {
                    "status": "success",
                    "message": "Task count received",
                    "server_task_count": server_task_count,
                    "client_task_count": client_task_count
                }
            else:
                return {"status": "error", "message": "Client not found"}

        except Exception as e:
            logger.error(f"处理客户端任务数量异常: {e}")
            return {"status": "error", "message": f"Handle task count failed: {str(e)}"}

    def get_photo_settings(self) -> Dict[str, Any]:
        """获取照片参数设置"""
        return {
            "status": "success",
            "photo_settings": self.photo_settings
        }

    def update_photo_settings(self, settings: Dict[str, Any]) -> bool:
        """更新照片参数设置"""
        try:
            self.photo_settings.update(settings)
            logger.info(f"照片参数已更新: {settings}")

            # 保存设置到文件
            self.save_photo_settings()

            # 向所有连接的客户端广播新的照片参数
            self.broadcast_photo_settings_to_clients()

            return True
        except Exception as e:
            logger.error(f"更新照片参数失败: {e}")
            return False

    def broadcast_photo_settings_to_clients(self):
        """向所有连接的客户端广播照片参数"""
        try:
            settings_message = {
                "type": "photo_settings_updated",
                "photo_settings": self.photo_settings
            }

            # 向所有连接的客户端发送更新消息
            disconnected_clients = []
            for client_id, client_info in self.connected_clients.items():
                try:
                    client_socket = client_info.get('socket')
                    if client_socket:
                        client_socket.send(json.dumps(settings_message).encode('utf-8'))
                        logger.info(f"已向客户端 {client_id} 发送照片参数更新")
                except Exception as e:
                    logger.error(f"向客户端 {client_id} 发送照片参数更新失败: {e}")
                    disconnected_clients.append(client_id)

            # 清理断开连接的客户端
            for client_id in disconnected_clients:
                if client_id in self.connected_clients:
                    del self.connected_clients[client_id]

        except Exception as e:
            logger.error(f"广播照片参数失败: {e}")

    def broadcast_test_mode_to_clients(self, is_test_mode: bool):
        """向所有连接的客户端广播测试模式状态"""
        try:
            test_mode_message = {
                "type": "test_mode_changed",
                "is_test_mode": is_test_mode,
                "message": "测试模式" if is_test_mode else "正式模式"
            }

            # 向所有连接的客户端发送更新消息
            disconnected_clients = []
            for client_id, client_info in self.connected_clients.items():
                try:
                    client_socket = client_info.get('socket')
                    if client_socket:
                        client_socket.send(json.dumps(test_mode_message).encode('utf-8'))
                        logger.info(f"已向客户端 {client_id} 发送测试模式更新: {'测试模式' if is_test_mode else '正式模式'}")
                except Exception as e:
                    logger.error(f"向客户端 {client_id} 发送测试模式更新失败: {e}")
                    disconnected_clients.append(client_id)

            # 清理断开连接的客户端
            for client_id in disconnected_clients:
                if client_id in self.connected_clients:
                    del self.connected_clients[client_id]

        except Exception as e:
            logger.error(f"广播测试模式失败: {e}")

    def send_test_mode_to_client(self, client_id: str, is_test_mode: bool):
        """向指定客户端发送测试模式状态"""
        try:
            if client_id not in self.connected_clients:
                logger.warning(f"客户端 {client_id} 不在连接列表中")
                return

            test_mode_message = {
                "type": "test_mode_changed",
                "is_test_mode": is_test_mode,
                "message": "测试模式" if is_test_mode else "正式模式"
            }

            client_socket = self.connected_clients[client_id].get('socket')
            if client_socket:
                client_socket.send(json.dumps(test_mode_message).encode('utf-8'))
                logger.info(f"已向客户端 {client_id} 发送测试模式状态: {'测试模式' if is_test_mode else '正式模式'}")
            else:
                logger.warning(f"客户端 {client_id} 的socket不可用")

        except Exception as e:
            logger.error(f"向客户端 {client_id} 发送测试模式状态失败: {e}")
            # 如果发送失败，可能客户端已断开，从连接列表中移除
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]

    def _send_no_task_message(self, client_id: str):
        """向客户端发送无任务消息"""
        try:
            if client_id not in self.connected_clients:
                logger.warning(f"客户端 {client_id} 不在连接列表中")
                return

            no_task_message = {
                "type": "no_tasks_available",
                "message": "当前没有可分配的任务",
                "timestamp": time.time()
            }

            client_socket = self.connected_clients[client_id].get('socket')
            if client_socket:
                client_socket.send(json.dumps(no_task_message).encode('utf-8'))
                logger.info(f"已向客户端 {client_id} 发送无任务消息")
            else:
                logger.warning(f"客户端 {client_id} 的socket不可用")

        except Exception as e:
            logger.error(f"向客户端 {client_id} 发送无任务消息失败: {e}")
            # 如果发送失败，可能客户端已断开，从连接列表中移除
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]

    def get_client_task_status(self, client_id: str) -> Dict[str, Any]:
        """获取客户端任务状态信息"""
        try:
            # 获取客户端当前的任务数量
            assigned_count = self.db.get_pc_client_incomplete_tasks_count(client_id)
            suspended_count = self.db.get_pc_client_suspended_tasks_count(client_id)
            total_tasks = assigned_count + suspended_count

            # 获取客户端的未完成任务列表
            incomplete_tasks = self.db.get_pc_client_incomplete_tasks(client_id)

            return {
                "client_id": client_id,
                "assigned_count": assigned_count,
                "suspended_count": suspended_count,
                "total_tasks": total_tasks,
                "incomplete_tasks": incomplete_tasks,
                "available_slots": max(0, 5 - total_tasks)
            }
        except Exception as e:
            logger.error(f"获取客户端任务状态失败: {e}")
            return {
                "client_id": client_id,
                "assigned_count": 0,
                "suspended_count": 0,
                "total_tasks": 0,
                "incomplete_tasks": [],
                "available_slots": 5
            }

    def assign_tasks_to_client(self, client_id: str) -> Dict[str, Any]:
        """为指定客户端分配任务（新的统一分配逻辑）

        1. 检查客户端当前任务数量
        2. 先发送该客户端的未完成任务
        3. 如果任务数量不足5个，再分配新任务

        Returns:
            Dict: 分配结果，包含发送的任务数量和详情
        """
        try:
            # 检查客户端是否还在连接状态
            if client_id not in self.connected_clients:
                logger.warning(f"客户端 {client_id} 已断开连接，无法分配任务")
                return {"status": "error", "message": "Client not connected"}

            # 检查客户端是否准备接收任务
            if not self.connected_clients[client_id].get('ready_for_tasks', False):
                logger.warning(f"客户端 {client_id} 尚未准备接收任务")
                return {"status": "error", "message": "Client not ready for tasks"}

            # 获取客户端任务状态
            task_status = self.get_client_task_status(client_id)
            total_tasks = task_status["total_tasks"]
            incomplete_tasks = task_status["incomplete_tasks"]
            available_slots = task_status["available_slots"]

            logger.info(f"客户端 {client_id} 任务状态: 总任务={total_tasks}/5, 可用槽位={available_slots}")

            sent_tasks = []

            # 检查是否已经达到任务数量限制
            if total_tasks >= 5:
                logger.debug(f"客户端 {client_id} 已有 {total_tasks} 个任务，达到上限，无法分配新任务")
                return {
                    "status": "success",
                    "client_id": client_id,
                    "sent_tasks_count": 0,
                    "incomplete_tasks_sent": 0,
                    "new_tasks_assigned": 0,
                    "final_task_count": total_tasks,
                    "sent_tasks": [],
                    "message": "Client task limit reached"
                }

            # 计算可以分配的新任务数量
            available_slots = 5 - total_tasks
            sent_tasks = []

            # 分配新任务填满槽位
            if available_slots > 0:
                logger.info(f"客户端 {client_id} 还有 {available_slots} 个可用槽位，开始分配新任务")

                # 获取未分配的任务
                unassigned_tasks = self.db.get_unassigned_tasks(self.task_source)

                assigned_new_count = 0
                for task in unassigned_tasks:
                    if assigned_new_count >= available_slots:
                        break

                    # 将任务分配给客户端
                    if self.db.assign_task_to_client(task["task_id"], client_id):
                        if self._send_task_to_client(client_id, task):
                            sent_tasks.append(task)
                            self.task_assigned.emit(task["task_id"], client_id)
                            assigned_new_count += 1
                            logger.info(f"分配新任务: {task['task_id']} -> {client_id} ({assigned_new_count}/{available_slots})")
                        else:
                            # 发送失败，回滚任务分配
                            self.db.reset_task_status(task["task_id"])
                            logger.error(f"发送任务失败，回滚分配: {task['task_id']}")
                            break

                logger.info(f"为客户端 {client_id} 分配了 {assigned_new_count} 个新任务")

            # 更新最终任务数量
            final_task_count = self.db.get_pc_client_incomplete_tasks_count(client_id) + self.db.get_pc_client_suspended_tasks_count(client_id)

            return {
                "status": "success",
                "client_id": client_id,
                "sent_tasks_count": len(sent_tasks),
                "incomplete_tasks_sent": 0,  # 不再发送未完成任务
                "new_tasks_assigned": len(sent_tasks),
                "final_task_count": final_task_count,
                "sent_tasks": sent_tasks
            }

        except Exception as e:
            logger.error(f"为客户端分配任务失败: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            return {"status": "error", "message": f"Assignment failed: {str(e)}"}

    def get_pending_task(self, client_id: str) -> Dict[str, Any]:
        """获取待处理任务（客户端主动请求）"""
        # 使用新的分配逻辑
        result = self.assign_tasks_to_client(client_id)

        if result.get("status") == "success":
            sent_tasks = result.get("sent_tasks", [])
            if sent_tasks:
                # 返回第一个任务（兼容原有接口）
                return {
                    "status": "success",
                    "task": sent_tasks[0],
                    "client_id": client_id,
                    "total_sent": len(sent_tasks)
                }
            else:
                return {"status": "no_task", "message": "No tasks available"}
        else:
            return result

    def assign_task_to_client(self, client_id: str):
        """主动分配任务给客户端（使用新的分配逻辑）"""
        result = self.assign_tasks_to_client(client_id)
        return result.get("status") == "success"

    def notify_task_deleted(self, task_id: str, client_id: str):
        """通知PC客户端任务已被删除"""
        try:
            client_socket = self.connected_clients.get(client_id, {}).get('socket')
            if client_socket:
                # 发送任务删除通知
                delete_message = {
                    "type": "task_deleted",
                    "task_id": task_id
                }
                client_socket.send(json.dumps(delete_message).encode('utf-8'))
                logger.info(f"已通知客户端 {client_id} 删除任务: {task_id}")
                return True
        except Exception as e:
            logger.error(f"通知客户端 {client_id} 删除任务失败: {e}")
            # 客户端可能已断开，从连接列表中移除
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]
        return False

    def check_and_assign_tasks(self):
        """检查并分配任务给所有连接的客户端（新的统一分配逻辑）"""
        try:
            # 获取所有连接且已准备接收任务的客户端
            ready_clients = []
            for client_id, client_info in self.connected_clients.items():
                if client_info.get('ready_for_tasks', False):
                    ready_clients.append(client_id)

            if not ready_clients:
                logger.debug("没有准备好接收任务的客户端")
                return

            logger.info(f"开始检查任务分配，找到 {len(ready_clients)} 个准备好的客户端: {ready_clients}")

            # 获取所有未分配的任务数量（用于日志）
            unassigned_tasks = self.db.get_unassigned_tasks(self.task_source)
            if unassigned_tasks:
                logger.info(f"数据库中有 {len(unassigned_tasks)} 个未分配任务")

            total_assigned = 0
            client_results = {}

            # 为每个客户端分配任务
            for client_id in ready_clients:
                try:
                    # 获取客户端当前状态
                    task_status = self.get_client_task_status(client_id)
                    logger.info(f"客户端 {client_id} (机位{self.get_client_station_number(client_id)}号): "
                              f"当前任务={task_status['total_tasks']}/5, "
                              f"未完成任务={len(task_status['incomplete_tasks'])}个, "
                              f"可用槽位={task_status['available_slots']}个")

                    # 如果客户端有可用槽位或有未完成任务需要发送
                    if task_status['available_slots'] > 0 or task_status['incomplete_tasks']:
                        result = self.assign_tasks_to_client(client_id)

                        if result.get("status") == "success":
                            sent_count = result.get("sent_tasks_count", 0)
                            incomplete_sent = result.get("incomplete_tasks_sent", 0)
                            new_assigned = result.get("new_tasks_assigned", 0)

                            total_assigned += new_assigned
                            client_results[client_id] = {
                                "sent_total": sent_count,
                                "incomplete_sent": incomplete_sent,
                                "new_assigned": new_assigned,
                                "final_count": result.get("final_task_count", 0)
                            }

                            if sent_count > 0:
                                logger.info(f"客户端 {client_id}: 发送了 {sent_count} 个任务 "
                                          f"(未完成: {incomplete_sent}, 新分配: {new_assigned})")
                        elif result.get("status") == "no_task":
                            logger.info(f"客户端 {client_id} 无可分配任务")
                            # 发送无任务消息给客户端
                            self._send_no_task_message(client_id)
                        else:
                            logger.warning(f"为客户端 {client_id} 分配任务失败: {result.get('message')}")
                    else:
                        logger.debug(f"客户端 {client_id} 已满载，无需分配任务")

                except Exception as e:
                    logger.error(f"为客户端 {client_id} 分配任务时出错: {e}")

            # 输出最终统计
            if total_assigned > 0 or any(r["sent_total"] > 0 for r in client_results.values()):
                logger.info(f"任务分配完成，本次新分配了 {total_assigned} 个任务")
                for client_id, result in client_results.items():
                    if result["sent_total"] > 0:
                        logger.info(f"  客户端 {client_id}: 最终任务数 {result['final_count']}/5")

        except Exception as e:
            logger.error(f"检查分配任务异常: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")

    def _send_task_to_client(self, client_id: str, task: Dict[str, Any]):
        """发送任务给客户端"""
        try:
            client_socket = self.connected_clients.get(client_id, {}).get('socket')
            if client_socket:
                # 发送任务分配消息
                task_message = {
                    "type": "task_assigned",
                    "task": task
                }
                client_socket.send(json.dumps(task_message).encode('utf-8'))
                # 注意：不在这里发送task_assigned信号，应该在调用方发送
                return True
        except Exception as e:
            logger.error(f"向客户端 {client_id} 发送任务失败: {e}")
            # 客户端可能已断开，从连接列表中移除
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]
        return False

    def assign_next_task_to_client(self, client_id: str):
        """为指定客户端分配下一个任务（使用新的分配逻辑）"""
        result = self.assign_tasks_to_client(client_id)
        return result.get("status") == "success" and result.get("sent_tasks_count", 0) > 0

    def handle_photo_upload(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理证件照上传"""
        try:
            task_id = message.get('task_id')
            photo_data_b64 = message.get('photo_data')  # base64编码的证件照
            photo_filename = message.get('photo_filename')
            
            if not all([task_id, photo_data_b64, photo_filename]):
                return {"status": "error", "message": "Missing required fields"}
            
            # 解码照片数据
            photo_data = base64.b64decode(photo_data_b64)
            
            # 保存到数据库
            success = self.db.save_photo(task_id, photo_data, photo_filename)
            
            if success:
                self.photo_uploaded.emit(task_id, client_id)

                # 主动发送成功消息给客户端
                self._send_upload_result_to_client(client_id, True, "照片上传成功")

                # 任务完成后，不立即分配新任务，让定时器来处理任务分配
                # 这样可以确保任务数量限制得到正确执行
                logger.info(f"任务 {task_id} 已完成，等待定时器分配新任务给客户端 {client_id}")

                return {"status": "success", "message": "Photo uploaded and task completed"}
            else:
                # 主动发送失败消息给客户端
                self._send_upload_result_to_client(client_id, False, "保存照片失败")

                return {"status": "error", "message": "Failed to save photo"}
                
        except Exception as e:
            logger.error(f"上传照片失败: {e}")
            # 主动发送失败消息给客户端
            self._send_upload_result_to_client(client_id, False, f"上传异常: {str(e)}")
            return {"status": "error", "message": f"Upload failed: {str(e)}"}

    def handle_test_task_complete(self, message: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """处理测试任务完成"""
        try:
            task_id = message.get('task_id')
            test_data = message.get('test_data', {})

            if not task_id:
                return {"status": "error", "message": "Missing task_id"}

            # 生成测试照片数据
            import base64
            import time

            # 创建一个简单的测试图片数据（1x1像素的JPEG）
            fake_jpeg_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'

            # 生成测试照片文件名
            timestamp = int(time.time())
            photo_filename = f"test_{task_id}_{timestamp}.jpg"

            # 保存到数据库
            success = self.db.save_photo(task_id, fake_jpeg_data, photo_filename)

            if success:
                self.photo_uploaded.emit(task_id, client_id)

                # 主动发送成功消息给客户端
                self._send_upload_result_to_client(client_id, True, "测试任务完成")

                logger.info(f"测试任务 {task_id} 已完成，客户端 {client_id}")

                return {"status": "success", "message": "Test task completed"}
            else:
                # 主动发送失败消息给客户端
                self._send_upload_result_to_client(client_id, False, "保存测试数据失败")

                return {"status": "error", "message": "Failed to save test data"}

        except Exception as e:
            logger.error(f"处理测试任务完成失败: {e}")
            # 主动发送失败消息给客户端
            self._send_upload_result_to_client(client_id, False, f"测试任务异常: {str(e)}")
            return {"status": "error", "message": f"Test task failed: {str(e)}"}

    def _send_upload_result_to_client(self, client_id: str, success: bool, message: str):
        """向客户端发送上传结果"""
        try:
            if client_id in self.connected_clients:
                client_socket = self.connected_clients[client_id].get('socket')
                if client_socket:
                    result_message = {
                        "type": "upload_result",
                        "success": success,
                        "message": message
                    }

                    client_socket.send(json.dumps(result_message).encode('utf-8'))
                    logger.info(f"已向客户端 {client_id} 发送上传结果: {success}, {message}")

        except Exception as e:
            logger.error(f"发送上传结果失败: {e}")

    def cancel_task(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """取消任务"""
        try:
            task_id = message.get('task_id')
            if not task_id:
                return {"status": "error", "message": "Missing task_id"}
            
            success = self.db.cancel_task(task_id)
            
            if success:
                self.task_cancelled.emit(task_id)
                return {"status": "success", "message": "Task cancelled and deleted"}
            else:
                return {"status": "error", "message": "Task not found"}
                
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return {"status": "error", "message": f"Cancel task failed: {str(e)}"}
    
    def get_client_info(self, client_id: str) -> Dict[str, Any]:
        """获取客户端信息"""
        if client_id in self.connected_clients:
            client_info = self.connected_clients[client_id]
            tasks = self.db.get_pc_client_tasks(client_id)
            
            return {
                "status": "success",
                "client_id": client_id,
                "address": client_info["address"],
                "connected_time": client_info["connected_time"].isoformat(),
                "tasks": tasks
            }
        else:
            return {"status": "error", "message": "Client not found"}
    
    def get_connected_clients_info(self) -> Dict[str, Any]:
        """获取所有连接的客户端信息"""
        clients_info = {}

        for client_id, info in self.connected_clients.items():
            # 格式化连接时间
            connected_time = info.get("connected_time", time.time())
            if isinstance(connected_time, (int, float)):
                time_str = datetime.fromtimestamp(connected_time).strftime("%Y-%m-%d %H:%M:%S")
            else:
                time_str = str(connected_time)

            # 获取未完成任务数量
            incomplete_tasks = self.db.get_pc_client_incomplete_tasks_count(client_id)
            total_tasks = len(self.db.get_pc_client_tasks(client_id))

            clients_info[client_id] = {
                "address": f"{info['address'][0]}:{info['address'][1]}",
                "connected_time": time_str,
                "tasks": total_tasks,
                "incomplete_tasks": incomplete_tasks,
                "station_number": info.get("station_number", 1)
            }

        return clients_info

    def load_photo_settings(self):
        """加载保存的照片设置"""
        try:
            if os.path.exists(config.PHOTO_SETTINGS_FILE):
                with open(config.PHOTO_SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.photo_settings.update(saved_settings)
                    logger.info("已加载保存的照片设置")
            else:
                logger.info("未找到保存的照片设置，使用默认设置")
        except Exception as e:
            logger.error(f"加载照片设置失败: {e}")

    def save_photo_settings(self):
        """保存照片设置到文件"""
        try:
            with open(config.PHOTO_SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.photo_settings, f, ensure_ascii=False, indent=2)
                logger.info("照片设置已保存")
        except Exception as e:
            logger.error(f"保存照片设置失败: {e}")

    def load_station_assignments(self):
        """加载机位号分配记录"""
        try:
            station_file = "station_assignments.json"
            if os.path.exists(station_file):
                with open(station_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.machine_stations = data.get("machine_stations", {})
                    self.next_station_number = data.get("next_station_number", 1)
                    logger.info(f"已加载机位号分配记录: {len(self.machine_stations)} 条")
            else:
                logger.info("未找到机位号分配记录，使用默认设置")
        except Exception as e:
            logger.error(f"加载机位号分配记录失败: {e}")

    def save_station_assignments(self):
        """保存机位号分配记录"""
        try:
            station_file = "station_assignments.json"
            data = {
                "machine_stations": self.machine_stations,
                "next_station_number": self.next_station_number
            }
            with open(station_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info("机位号分配记录已保存")
        except Exception as e:
            logger.error(f"保存机位号分配记录失败: {e}")

    def assign_station_by_machine_id(self, machine_id: str, client_id: str) -> int:
        """根据机器码分配机位号"""
        try:
            if not machine_id:
                logger.warning(f"客户端 {client_id} 没有提供机器码，分配默认机位号")
                return 1

            # 检查是否已经为这个机器码分配过机位号
            if machine_id in self.machine_stations:
                station_number = self.machine_stations[machine_id]
                self.client_stations[client_id] = station_number
                logger.info(f"机器码 {machine_id} 重用机位号: {station_number}")
                return station_number

            # 分配新的机位号
            station_number = self.next_station_number
            self.next_station_number += 1

            # 保存机器码到机位号的映射
            self.machine_stations[machine_id] = station_number
            self.client_stations[client_id] = station_number

            # 保存分配记录
            self.save_station_assignments()

            logger.info(f"为机器码 {machine_id} 分配新机位号: {station_number}")
            return station_number

        except Exception as e:
            logger.error(f"根据机器码分配机位号失败: {e}")
            return 1  # 默认返回1号机位

    def update_client_station_number(self, client_id: str, new_station_number: int):
        """更新客户端机位号并同步到PC端"""
        try:
            if client_id in self.connected_clients:
                old_station = self.connected_clients[client_id].get('station_number', 0)
                self.connected_clients[client_id]['station_number'] = new_station_number

                # 发送机位号更新消息给PC端
                client_socket = self.connected_clients[client_id].get('socket')
                if client_socket:
                    update_message = {
                        "type": "station_number_update",
                        "station_number": new_station_number
                    }
                    try:
                        client_socket.send(json.dumps(update_message).encode('utf-8'))
                        logger.info(f"已向客户端 {client_id} 发送机位号更新: {old_station} -> {new_station_number}")
                    except Exception as e:
                        logger.error(f"向客户端 {client_id} 发送机位号更新失败: {e}")

                return True
            else:
                logger.warning(f"客户端 {client_id} 不在连接列表中")
                return False

        except Exception as e:
            logger.error(f"更新客户端机位号失败: {e}")
            return False

    def assign_station_number(self, client_id: str, address: tuple) -> int:
        """为客户端分配机位号"""
        try:
            # 根据IP地址生成唯一标识
            client_ip = address[0]

            # 检查是否已经为这个IP分配过机位号（查找活跃的分配）
            for existing_client_id, client_info in self.connected_clients.items():
                if (existing_client_id != client_id and
                    client_info.get('address', [''])[0] == client_ip and
                    'station_number' in client_info):
                    # 找到了相同IP的活跃连接，重用机位号
                    station_num = client_info['station_number']
                    self.client_stations[client_id] = station_num
                    logger.info(f"为客户端 {client_id} ({client_ip}) 重用活跃机位号: {station_num}")
                    return station_num

            # 检查历史分配记录
            for existing_client_id, station_num in self.client_stations.items():
                if existing_client_id.startswith(f"station_{client_ip}_"):
                    # 找到了相同IP的历史记录，重用机位号
                    self.client_stations[client_id] = station_num
                    logger.info(f"为客户端 {client_id} ({client_ip}) 重用历史机位号: {station_num}")
                    return station_num

            # 没有找到记录，分配新的机位号
            station_number = self.next_station_number
            self.next_station_number += 1

            # 保存机位号分配记录
            station_key = f"station_{client_ip}_{station_number}"
            self.client_stations[station_key] = station_number
            self.client_stations[client_id] = station_number

            logger.info(f"为客户端 {client_id} ({client_ip}) 分配新机位号: {station_number}")
            return station_number

        except Exception as e:
            logger.error(f"分配机位号失败: {e}")
            return 1  # 默认返回1号机位

    def get_client_station_number(self, client_id: str) -> int:
        """获取客户端的机位号"""
        return self.client_stations.get(client_id, 1)

    def clear_station_assignments(self) -> int:
        """清空所有机位号分配记录"""
        try:
            # 统计清空前的记录数量
            cleared_count = len(self.machine_stations) + len(self.client_stations)

            # 清空内存中的分配记录
            self.machine_stations.clear()
            self.client_stations.clear()

            # 重置下一个机位号
            self.next_station_number = 1

            # 保存清空后的状态
            self.save_station_assignments()

            # 删除分配文件（如果存在）
            import os
            station_file = "station_assignments.json"
            if os.path.exists(station_file):
                os.remove(station_file)
                logger.info("已删除机位号分配文件")

            logger.info(f"清空机位号分配成功: {cleared_count} 个分配记录")
            return cleared_count

        except Exception as e:
            logger.error(f"清空机位号分配失败: {e}")
            raise e

    def reset_station_assignments_for_connected_clients(self):
        """为当前连接的客户端重新分配机位号"""
        try:
            # 清空当前分配
            self.clear_station_assignments()

            # 为当前连接的客户端重新分配机位号
            for client_id, client_info in self.connected_clients.items():
                machine_id = client_info.get('machine_id')
                if machine_id:
                    new_station = self.assign_station_by_machine_id(machine_id, client_id)
                    client_info['station_number'] = new_station

                    # 通知客户端新的机位号
                    self.update_client_station_number(client_id, new_station)

            logger.info("已为所有连接的客户端重新分配机位号")

        except Exception as e:
            logger.error(f"重新分配机位号失败: {e}")
            raise e
