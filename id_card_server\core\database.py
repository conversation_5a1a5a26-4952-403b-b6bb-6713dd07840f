#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单表数据库管理
"""

import sqlite3
import os
import uuid
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)

class DatabaseManager:
    """单表数据库管理器"""
    
    def __init__(self, db_path="photo_tasks.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建正式任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS photo_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id VARCHAR(50) NOT NULL UNIQUE,
                    pc_client_id VARCHAR(50),
                    name VARCHAR(30) NOT NULL,
                    gender VARCHAR(2) NOT NULL,
                    birth_date VARCHAR(16) NOT NULL,
                    id_number VARCHAR(36) NOT NULL,
                    photo_data BLOB,
                    photo_filename VARCHAR(255),
                    task_status VARCHAR(20) DEFAULT 'pending',
                    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    assigned_time DATETIME,
                    completed_time DATETIME
                )
            ''')

            # 创建测试任务表（结构相同）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS test_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id VARCHAR(50) NOT NULL UNIQUE,
                    pc_client_id VARCHAR(50),
                    name VARCHAR(30) NOT NULL,
                    gender VARCHAR(2) NOT NULL,
                    birth_date VARCHAR(16) NOT NULL,
                    id_number VARCHAR(36) NOT NULL,
                    photo_data BLOB,
                    photo_filename VARCHAR(255),
                    task_status VARCHAR(20) DEFAULT 'pending',
                    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    assigned_time DATETIME,
                    completed_time DATETIME
                )
            ''')

            # 为正式任务表创建索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_task_status ON photo_tasks(task_status)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_id_number ON photo_tasks(id_number)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_pc_client_id ON photo_tasks(pc_client_id)
            ''')

            # 为测试任务表创建索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_test_task_status ON test_tasks(task_status)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_test_id_number ON test_tasks(id_number)
            ''')
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_test_pc_client_id ON test_tasks(pc_client_id)
            ''')

            conn.commit()
            conn.close()
            logger.info("数据库初始化成功（包含正式任务表和测试任务表）")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise e

    def get_table_name_by_task_id(self, task_id: str) -> str:
        """根据任务ID判断来源表"""
        if task_id.startswith("TEST_"):
            return "test_tasks"
        else:
            return "photo_tasks"

    def check_id_number_exists(self, id_number: str) -> bool:
        """检查身份证号码是否已存在"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT COUNT(*) FROM photo_tasks WHERE id_number = ?
            ''', (id_number,))

            count = cursor.fetchone()[0]
            return count > 0

        except Exception as e:
            logger.error(f"检查身份证号码异常: {e}")
            return False
        finally:
            conn.close()

    def get_task_by_id_number(self, id_number: str) -> Optional[Dict[str, Any]]:
        """根据身份证号码获取任务信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT task_id, name, gender, birth_date, id_number,
                       task_status, pc_client_id, created_time,
                       assigned_time, completed_time
                FROM photo_tasks
                WHERE id_number = ?
                ORDER BY created_time DESC
                LIMIT 1
            ''', (id_number,))

            task = cursor.fetchone()
            if task:
                return {
                    "task_id": task[0],
                    "name": task[1],
                    "gender": task[2],
                    "birth_date": task[3],
                    "id_number": task[4],
                    "task_status": task[5],
                    "pc_client_id": task[6],
                    "created_time": task[7],
                    "assigned_time": task[8],
                    "completed_time": task[9]
                }
            return None

        except Exception as e:
            logger.error(f"根据身份证号码获取任务异常: {e}")
            return None
        finally:
            conn.close()

    def create_task(self, card_info: Dict[str, str]) -> str:
        """创建新任务"""
        # 检查身份证号码是否已存在
        if self.check_id_number_exists(card_info['id_number']):
            logger.warning(f"身份证号码已存在: {card_info['id_number']}")
            return None

        task_id = str(uuid.uuid4())

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO photo_tasks
                (task_id, name, gender, birth_date, id_number, task_status)
                VALUES (?, ?, ?, ?, ?, 'pending')
            ''', (
                task_id,
                card_info['name'],
                card_info['gender'],
                card_info['birth_date'],
                card_info['id_number']
            ))

            conn.commit()
            logger.info(f"创建任务成功: {task_id} - {card_info['name']}")
            return task_id

        except sqlite3.IntegrityError as e:
            conn.rollback()
            logger.error(f"创建任务失败，可能重复: {e}")
            raise e
        except Exception as e:
            conn.rollback()
            logger.error(f"创建任务异常: {e}")
            raise e
        finally:
            conn.close()
    

    
    def save_photo(self, task_id: str, photo_data: bytes, photo_filename: str) -> bool:
        """保存证件照"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                UPDATE photo_tasks 
                SET photo_data = ?, 
                    photo_filename = ?,
                    task_status = 'completed',
                    completed_time = CURRENT_TIMESTAMP
                WHERE task_id = ?
            ''', (photo_data, photo_filename, task_id))
            
            conn.commit()
            success = cursor.rowcount > 0
            
            if success:
                logger.info(f"保存照片成功: {task_id} - {photo_filename}")
            else:
                logger.warning(f"保存照片失败，任务不存在: {task_id}")
            
            return success
            
        except Exception as e:
            conn.rollback()
            logger.error(f"保存照片异常: {e}")
            raise e
        finally:
            conn.close()
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务 - 直接删除记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 根据任务ID判断来源表
            table_name = self.get_table_name_by_task_id(task_id)

            query = f'DELETE FROM {table_name} WHERE task_id = ?'
            cursor.execute(query, (task_id,))

            conn.commit()
            success = cursor.rowcount > 0

            if success:
                logger.info(f"取消任务成功: {task_id}")
            else:
                logger.warning(f"取消任务失败，任务不存在: {task_id}")

            return success

        except Exception as e:
            conn.rollback()
            logger.error(f"取消任务异常: {e}")
            raise e
        finally:
            conn.close()

    def delete_task(self, task_id: str) -> dict:
        """删除指定任务，返回删除信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 根据任务ID判断来源表
            table_name = self.get_table_name_by_task_id(task_id)

            # 先获取任务信息（包括分配的客户端ID）
            query = f'SELECT pc_client_id, task_status FROM {table_name} WHERE task_id = ?'
            cursor.execute(query, (task_id,))
            task_info = cursor.fetchone()

            if not task_info:
                logger.warning(f"任务 {task_id} 不存在")
                return {"success": False, "message": "任务不存在"}

            pc_client_id, task_status = task_info

            # 删除任务
            delete_query = f'DELETE FROM {table_name} WHERE task_id = ?'
            cursor.execute(delete_query, (task_id,))
            conn.commit()

            if cursor.rowcount > 0:
                logger.info(f"任务 {task_id} 已删除")
                return {
                    "success": True,
                    "task_id": task_id,
                    "pc_client_id": pc_client_id,
                    "status": task_status,
                    "message": "任务删除成功"
                }
            else:
                logger.warning(f"任务 {task_id} 删除失败")
                return {"success": False, "message": "任务删除失败"}

        except Exception as e:
            logger.error(f"删除任务异常: {e}")
            return {"success": False, "message": f"删除任务异常: {str(e)}"}
        finally:
            conn.close()

    def delete_task_by_id(self, task_id: str) -> bool:
        """根据任务ID删除任务"""
        return self.cancel_task(task_id)

    def delete_task_by_id_number(self, id_number: str) -> bool:
        """根据身份证号码删除任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                DELETE FROM photo_tasks WHERE id_number = ?
            ''', (id_number,))

            conn.commit()
            success = cursor.rowcount > 0

            if success:
                logger.info(f"删除任务成功: 身份证号 {id_number}")
            else:
                logger.warning(f"删除任务失败，身份证号不存在: {id_number}")

            return success

        except Exception as e:
            conn.rollback()
            logger.error(f"删除任务异常: {e}")
            raise e
        finally:
            conn.close()

    def delete_multiple_tasks(self, task_ids: List[str]) -> int:
        """批量删除任务"""
        if not task_ids:
            return 0

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 使用IN语句批量删除
            placeholders = ','.join(['?' for _ in task_ids])
            cursor.execute(f'''
                DELETE FROM photo_tasks WHERE task_id IN ({placeholders})
            ''', task_ids)

            conn.commit()
            deleted_count = cursor.rowcount

            logger.info(f"批量删除任务成功: {deleted_count} 个任务")
            return deleted_count

        except Exception as e:
            conn.rollback()
            logger.error(f"批量删除任务异常: {e}")
            raise e
        finally:
            conn.close()

    def delete_all_pending_tasks(self) -> int:
        """删除所有未完成的任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                DELETE FROM photo_tasks WHERE task_status IN ('pending', 'assigned')
            ''')

            conn.commit()
            deleted_count = cursor.rowcount

            logger.info(f"删除所有未完成任务成功: {deleted_count} 个任务")
            return deleted_count

        except Exception as e:
            conn.rollback()
            logger.error(f"删除所有未完成任务异常: {e}")
            raise e
        finally:
            conn.close()
    
    def get_completed_tasks(self) -> List[tuple]:
        """获取所有已完成的任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT task_id, name, gender, birth_date, id_number, 
                       photo_data, photo_filename, completed_time
                FROM photo_tasks
                WHERE task_status = 'completed' AND photo_data IS NOT NULL
                ORDER BY completed_time DESC
            ''')
            
            tasks = cursor.fetchall()
            logger.info(f"获取已完成任务: {len(tasks)} 个")
            return tasks
            
        except Exception as e:
            logger.error(f"获取已完成任务异常: {e}")
            raise e
        finally:
            conn.close()
    
    def get_task_statistics(self, table_name: str = "photo_tasks", log_changes_only: bool = False) -> Dict[str, int]:
        """获取任务统计"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 验证表名安全性
            if table_name not in ["photo_tasks", "test_tasks"]:
                raise ValueError(f"无效的表名: {table_name}")

            query = f'''
                SELECT
                    COUNT(*) as total,
                    COALESCE(SUM(CASE WHEN task_status = 'pending' THEN 1 ELSE 0 END), 0) as pending,
                    COALESCE(SUM(CASE WHEN task_status = 'assigned' THEN 1 ELSE 0 END), 0) as assigned,
                    COALESCE(SUM(CASE WHEN task_status = 'completed' THEN 1 ELSE 0 END), 0) as completed,
                    COALESCE(SUM(CASE WHEN task_status = 'suspended' THEN 1 ELSE 0 END), 0) as suspended
                FROM {table_name}
            '''
            cursor.execute(query)

            stats = cursor.fetchone()

            result = {
                "total": stats[0],
                "pending": stats[1],
                "assigned": stats[2],
                "completed": stats[3],
                "suspended": stats[4]
            }

            # 只在有变化时记录日志，或者明确要求记录时
            if log_changes_only:
                # 检查是否有变化
                cache_key = f"last_stats_{table_name}"
                if not hasattr(self, cache_key) or getattr(self, cache_key) != result:
                    logger.info(f"任务统计 ({table_name}): {result}")
                    setattr(self, cache_key, result.copy())
            else:
                # 总是记录日志（用于手动调用的情况）
                logger.info(f"任务统计 ({table_name}): {result}")

            return result

        except Exception as e:
            logger.error(f"获取任务统计异常: {e}")
            raise e
        finally:
            conn.close()
    
    def get_pc_client_tasks(self, pc_client_id: str) -> List[tuple]:
        """获取指定PC端的任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT task_id, name, id_number, task_status, assigned_time, completed_time
                FROM photo_tasks
                WHERE pc_client_id = ?
                ORDER BY assigned_time DESC
            ''', (pc_client_id,))

            tasks = cursor.fetchall()
            logger.info(f"PC端 {pc_client_id} 的任务: {len(tasks)} 个")
            return tasks

        except Exception as e:
            logger.error(f"获取PC端任务异常: {e}")
            raise e
        finally:
            conn.close()

    def get_pc_client_incomplete_tasks_count(self, pc_client_id: str) -> int:
        """获取指定PC端的未完成任务数量（仅assigned状态）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT COUNT(*)
                FROM photo_tasks
                WHERE pc_client_id = ? AND task_status = 'assigned'
            ''', (pc_client_id,))

            count = cursor.fetchone()[0]
            logger.debug(f"PC端 {pc_client_id} 的未完成任务数量: {count}")
            return count

        except Exception as e:
            logger.error(f"获取PC端未完成任务数量异常: {e}")
            return 0
        finally:
            conn.close()

    def get_pc_client_suspended_tasks_count(self, pc_client_id: str) -> int:
        """获取指定PC端的暂停任务数量"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT COUNT(*)
                FROM photo_tasks
                WHERE pc_client_id = ? AND task_status = 'suspended'
            ''', (pc_client_id,))

            count = cursor.fetchone()[0]
            logger.debug(f"PC端 {pc_client_id} 的暂停任务数量: {count}")
            return count

        except Exception as e:
            logger.error(f"获取PC端暂停任务数量异常: {e}")
            return 0
        finally:
            conn.close()

    def get_pc_client_incomplete_tasks(self, pc_client_id: str) -> List[Dict[str, Any]]:
        """获取指定PC端的所有未完成任务（assigned和suspended状态）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT task_id, name, gender, birth_date, id_number, task_status, assigned_time
                FROM photo_tasks
                WHERE pc_client_id = ? AND task_status IN ('assigned', 'suspended')
                ORDER BY assigned_time ASC
            ''', (pc_client_id,))

            tasks = cursor.fetchall()
            result = []

            for task in tasks:
                result.append({
                    "task_id": task[0],
                    "name": task[1],
                    "gender": task[2],
                    "birth_date": task[3],
                    "id_number": task[4],
                    "task_status": task[5],
                    "assigned_time": task[6]
                })

            logger.info(f"获取PC端 {pc_client_id} 的未完成任务: {len(result)} 个")
            return result

        except Exception as e:
            logger.error(f"获取PC端未完成任务异常: {e}")
            return []
        finally:
            conn.close()

    def reassign_task_to_pending(self, task_id: str) -> bool:
        """将指定任务重新设置为待分配状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 根据任务ID判断来源表
            table_name = self.get_table_name_by_task_id(task_id)

            # 检查任务是否存在
            query = f'''
                SELECT task_status, pc_client_id FROM {table_name}
                WHERE task_id = ?
            '''
            cursor.execute(query, (task_id,))

            result = cursor.fetchone()
            if not result:
                logger.warning(f"任务 {task_id} 不存在")
                return False

            task_status, current_client = result

            # 将任务重新设置为待分配状态
            update_query = f'''
                UPDATE {table_name}
                SET task_status = 'pending', pc_client_id = NULL, assigned_time = NULL
                WHERE task_id = ?
            '''
            cursor.execute(update_query, (task_id,))

            conn.commit()

            if cursor.rowcount > 0:
                logger.info(f"任务 {task_id} 已重新设置为待分配状态 (原状态: {task_status}, 原客户端: {current_client})")
                return True
            else:
                logger.warning(f"任务 {task_id} 重新分配失败")
                return False

        except Exception as e:
            logger.error(f"重新分配任务异常: {e}")
            return False
        finally:
            conn.close()

    def reassign_incomplete_tasks(self, pc_client_id: str) -> int:
        """重新分配指定PC端的未完成任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 将该PC端的未完成任务重新设置为待分配状态
            cursor.execute('''
                UPDATE photo_tasks
                SET task_status = 'pending',
                    pc_client_id = NULL,
                    assigned_time = NULL
                WHERE pc_client_id = ? AND task_status = 'assigned'
            ''', (pc_client_id,))

            reassigned_count = cursor.rowcount
            conn.commit()

            logger.info(f"重新分配PC端 {pc_client_id} 的 {reassigned_count} 个未完成任务")
            return reassigned_count

        except Exception as e:
            logger.error(f"重新分配任务异常: {e}")
            return 0
        finally:
            conn.close()

    def cancel_and_delete_client_tasks(self, pc_client_id: str) -> int:
        """取消并删除指定PC端的未完成任务（主动断开时使用）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            deleted_count = 0

            # 删除正式任务表中的未完成任务
            cursor.execute('''
                DELETE FROM photo_tasks
                WHERE pc_client_id = ? AND task_status IN ('assigned', 'suspended') AND photo_data IS NULL
            ''', (pc_client_id,))
            deleted_count += cursor.rowcount

            # 删除测试任务表中的未完成任务
            cursor.execute('''
                DELETE FROM test_tasks
                WHERE pc_client_id = ? AND task_status IN ('assigned', 'suspended') AND photo_data IS NULL
            ''', (pc_client_id,))
            deleted_count += cursor.rowcount

            conn.commit()

            logger.info(f"删除PC端 {pc_client_id} 的 {deleted_count} 个未完成任务")
            return deleted_count

        except Exception as e:
            logger.error(f"删除任务异常: {e}")
            return 0
        finally:
            conn.close()

    def suspend_client_tasks_for_reconnect(self, pc_client_id: str) -> int:
        """暂停指定PC端的未完成任务（网络异常断开时使用，等待重连）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            suspended_count = 0

            # 暂停正式任务表中的任务
            cursor.execute('''
                UPDATE photo_tasks
                SET task_status = 'suspended'
                WHERE pc_client_id = ? AND task_status = 'assigned'
            ''', (pc_client_id,))
            suspended_count += cursor.rowcount

            # 暂停测试任务表中的任务
            cursor.execute('''
                UPDATE test_tasks
                SET task_status = 'suspended'
                WHERE pc_client_id = ? AND task_status = 'assigned'
            ''', (pc_client_id,))
            suspended_count += cursor.rowcount

            conn.commit()

            logger.info(f"暂停PC端 {pc_client_id} 的 {suspended_count} 个任务，等待重连")
            return suspended_count

        except Exception as e:
            logger.error(f"暂停任务异常: {e}")
            return 0
        finally:
            conn.close()

    def reset_client_tasks_to_pending(self, pc_client_id: str) -> int:
        """将指定PC端的未完成任务重置为未分配状态（备用方法）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            reset_count = 0

            # 重置正式任务表中的任务
            cursor.execute('''
                UPDATE photo_tasks
                SET task_status = 'pending', pc_client_id = NULL, assigned_time = NULL
                WHERE pc_client_id = ? AND task_status IN ('assigned', 'suspended')
            ''', (pc_client_id,))
            reset_count += cursor.rowcount

            # 重置测试任务表中的任务
            cursor.execute('''
                UPDATE test_tasks
                SET task_status = 'pending', pc_client_id = NULL, assigned_time = NULL
                WHERE pc_client_id = ? AND task_status IN ('assigned', 'suspended')
            ''', (pc_client_id,))
            reset_count += cursor.rowcount

            conn.commit()

            logger.info(f"重置PC端 {pc_client_id} 的 {reset_count} 个任务为未分配状态")
            return reset_count

        except Exception as e:
            logger.error(f"重置任务状态异常: {e}")
            return 0
        finally:
            conn.close()

    def suspend_client_tasks(self, pc_client_id: str) -> int:
        """暂停指定PC端的未完成任务（标记为暂停状态）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 将该PC端的未完成任务标记为暂停状态
            cursor.execute('''
                UPDATE photo_tasks
                SET task_status = 'suspended'
                WHERE pc_client_id = ? AND task_status = 'assigned'
            ''', (pc_client_id,))

            suspended_count = cursor.rowcount
            conn.commit()

            logger.info(f"暂停PC端 {pc_client_id} 的 {suspended_count} 个未完成任务")
            return suspended_count

        except Exception as e:
            logger.error(f"暂停任务异常: {e}")
            return 0
        finally:
            conn.close()

    def resume_client_tasks(self, pc_client_id: str) -> List[Dict[str, Any]]:
        """恢复指定PC端的暂停任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 将该PC端的暂停任务恢复为分配状态
            cursor.execute('''
                UPDATE photo_tasks
                SET task_status = 'assigned', assigned_time = CURRENT_TIMESTAMP
                WHERE pc_client_id = ? AND task_status = 'suspended'
            ''', (pc_client_id,))

            resumed_count = cursor.rowcount
            conn.commit()

            if resumed_count > 0:
                # 获取恢复的任务列表
                cursor.execute('''
                    SELECT task_id, name, gender, birth_date, id_number,
                           task_status, pc_client_id, created_time,
                           assigned_time, completed_time
                    FROM photo_tasks
                    WHERE pc_client_id = ? AND task_status = 'assigned'
                    ORDER BY assigned_time ASC
                ''', (pc_client_id,))

                tasks = []
                for row in cursor.fetchall():
                    task_id, name, gender, birth_date, id_number, task_status, pc_client_id, created_time, assigned_time, completed_time = row
                    tasks.append({
                        "task_id": task_id,
                        "name": name,
                        "gender": gender,
                        "birth_date": birth_date,
                        "id_number": id_number,
                        "task_status": task_status,
                        "pc_client_id": pc_client_id,
                        "created_time": created_time,
                        "assigned_time": assigned_time,
                        "completed_time": completed_time
                    })

                logger.info(f"恢复PC端 {pc_client_id} 的 {resumed_count} 个暂停任务")
                return tasks

            return []

        except Exception as e:
            logger.error(f"恢复任务异常: {e}")
            return []
        finally:
            conn.close()
    
    def get_all_tasks(self, table_name: str = "photo_tasks") -> List[tuple]:
        """获取所有任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 验证表名安全性
            if table_name not in ["photo_tasks", "test_tasks"]:
                raise ValueError(f"无效的表名: {table_name}")

            query = f'''
                SELECT task_id, name, gender, birth_date, id_number,
                       task_status, pc_client_id, created_time,
                       assigned_time, completed_time
                FROM {table_name}
                ORDER BY created_time DESC
            '''
            cursor.execute(query)

            tasks = cursor.fetchall()
            return tasks

        except Exception as e:
            logger.error(f"获取所有任务异常: {e}")
            raise e
        finally:
            conn.close()

    def get_unassigned_tasks(self, table_name: str = "photo_tasks"):
        """获取所有未分配的任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 验证表名安全性
            if table_name not in ["photo_tasks", "test_tasks"]:
                raise ValueError(f"无效的表名: {table_name}")

            query = f'''
                SELECT task_id, name, gender, birth_date, id_number,
                       task_status, pc_client_id, created_time,
                       assigned_time, completed_time
                FROM {table_name}
                WHERE task_status = 'pending' AND (pc_client_id IS NULL OR pc_client_id = '')
                ORDER BY created_time ASC
            '''
            cursor.execute(query)

            tasks = []
            for row in cursor.fetchall():
                task_id, name, gender, birth_date, id_number, task_status, pc_client_id, created_time, assigned_time, completed_time = row
                tasks.append({
                    "task_id": task_id,
                    "name": name,
                    "gender": gender,
                    "birth_date": birth_date,
                    "id_number": id_number,
                    "task_status": task_status,
                    "pc_client_id": pc_client_id,
                    "created_time": created_time,
                    "assigned_time": assigned_time,
                    "completed_time": completed_time
                })

            return tasks

        except Exception as e:
            logger.error(f"获取未分配任务异常: {e}")
            return []
        finally:
            conn.close()

    def get_incomplete_tasks(self):
        """获取所有未完成的任务（无照片记录的任务都视为未完成）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT task_id, name, gender, birth_date, id_number,
                       task_status, pc_client_id, created_time,
                       assigned_time, completed_time
                FROM photo_tasks
                WHERE photo_data IS NULL OR photo_data = ''
                ORDER BY created_time ASC
            ''')

            tasks = []
            for row in cursor.fetchall():
                task_id, name, gender, birth_date, id_number, task_status, pc_client_id, created_time, assigned_time, completed_time = row
                tasks.append({
                    "task_id": task_id,
                    "name": name,
                    "gender": gender,
                    "birth_date": birth_date,
                    "id_number": id_number,
                    "task_status": task_status,
                    "pc_client_id": pc_client_id,
                    "created_time": created_time,
                    "assigned_time": assigned_time,
                    "completed_time": completed_time
                })

            return tasks

        except Exception as e:
            logger.error(f"获取未完成任务异常: {e}")
            return []
        finally:
            conn.close()

    def reset_incomplete_tasks_status(self):
        """重置所有已分配但无照片的任务状态为pending并清除分配信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 只重置那些已分配但没有照片的任务
            cursor.execute('''
                UPDATE photo_tasks
                SET task_status = 'pending',
                    pc_client_id = NULL,
                    assigned_time = NULL
                WHERE task_status = 'assigned'
                  AND (photo_data IS NULL OR photo_data = '')
            ''')

            reset_count = cursor.rowcount
            conn.commit()

            logger.info(f"重置了 {reset_count} 个已分配但无照片的任务状态")
            return reset_count

        except Exception as e:
            logger.error(f"重置未完成任务状态异常: {e}")
            return 0
        finally:
            conn.close()

    def assign_task_to_client(self, task_id: str, client_id: str) -> bool:
        """将任务分配给指定客户端"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 根据任务ID判断来源表
            table_name = self.get_table_name_by_task_id(task_id)

            # 检查任务是否存在且未分配
            query = f'''
                SELECT task_status, pc_client_id FROM {table_name}
                WHERE task_id = ?
            '''
            cursor.execute(query, (task_id,))

            result = cursor.fetchone()
            if not result:
                logger.warning(f"任务 {task_id} 不存在")
                return False

            task_status, current_client = result
            if task_status != 'pending':
                logger.warning(f"任务 {task_id} 状态不是pending，当前状态: {task_status}")
                return False

            if current_client and current_client.strip():
                logger.warning(f"任务 {task_id} 已分配给客户端: {current_client}")
                return False

            # 分配任务给客户端
            update_query = f'''
                UPDATE {table_name}
                SET pc_client_id = ?, task_status = 'assigned', assigned_time = CURRENT_TIMESTAMP
                WHERE task_id = ?
            '''
            cursor.execute(update_query, (client_id, task_id))

            conn.commit()

            if cursor.rowcount > 0:
                logger.info(f"任务 {task_id} 已分配给客户端 {client_id}")
                return True
            else:
                logger.warning(f"分配任务 {task_id} 给客户端 {client_id} 失败")
                return False

        except Exception as e:
            logger.error(f"分配任务异常: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def generate_test_tasks(self, count: int) -> bool:
        """生成测试任务"""
        import random
        import string
        from datetime import datetime, timedelta

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 测试用的姓名列表
            first_names = ["张", "王", "李", "赵", "刘", "陈", "杨", "黄", "周", "吴", "徐", "孙", "马", "朱", "胡", "郭", "何", "高", "林", "罗"]
            second_names = ["伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀", "霞", "平"]

            success_count = 0

            for i in range(count):
                # 生成随机姓名
                name = random.choice(first_names) + random.choice(second_names) + (random.choice(second_names) if random.random() > 0.5 else "")

                # 生成随机性别
                gender = random.choice(["男", "女"])

                # 生成随机出生日期（1970-2005年）
                start_date = datetime(1970, 1, 1)
                end_date = datetime(2005, 12, 31)
                random_date = start_date + timedelta(days=random.randint(0, (end_date - start_date).days))
                birth_date = random_date.strftime("%Y%m%d")

                # 生成随机身份证号码（测试用，格式正确但非真实）
                area_code = random.choice(["110101", "310101", "440101", "500101", "120101"])  # 北京、上海、广州、重庆、天津
                sequence = f"{random.randint(1, 999):03d}"
                check_digit = random.choice(string.digits + "X")
                id_number = area_code + birth_date + sequence + check_digit

                # 生成任务ID
                task_id = f"TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i+1:03d}"

                try:
                    cursor.execute('''
                        INSERT INTO test_tasks (
                            task_id, name, gender, birth_date, id_number,
                            task_status, created_time
                        ) VALUES (?, ?, ?, ?, ?, 'pending', CURRENT_TIMESTAMP)
                    ''', (task_id, name, gender, birth_date, id_number))

                    success_count += 1

                except sqlite3.IntegrityError:
                    # 如果ID重复，跳过这个任务
                    logger.warning(f"测试任务ID重复，跳过: {task_id}")
                    continue

            conn.commit()
            logger.info(f"成功生成 {success_count} 个测试任务")
            return success_count > 0

        except Exception as e:
            logger.error(f"生成测试任务异常: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def restore_suspended_tasks_for_client(self, pc_client_id: str) -> int:
        """恢复指定PC端的暂停任务（重连时使用）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            restored_count = 0

            # 恢复正式任务表中的暂停任务
            cursor.execute('''
                UPDATE photo_tasks
                SET task_status = 'assigned'
                WHERE pc_client_id = ? AND task_status = 'suspended'
            ''', (pc_client_id,))
            restored_count += cursor.rowcount

            # 恢复测试任务表中的暂停任务
            cursor.execute('''
                UPDATE test_tasks
                SET task_status = 'assigned'
                WHERE pc_client_id = ? AND task_status = 'suspended'
            ''', (pc_client_id,))
            restored_count += cursor.rowcount

            conn.commit()

            logger.info(f"恢复PC端 {pc_client_id} 的 {restored_count} 个暂停任务")
            return restored_count

        except Exception as e:
            logger.error(f"恢复暂停任务异常: {e}")
            return 0
        finally:
            conn.close()
