#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务端连接客户端
"""

import socket
import json
import threading
import time
import logging
import hashlib
import platform
import uuid
import subprocess
from typing import Dict, Any, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal
import config

logger = logging.getLogger(__name__)

def generate_hardware_device_id():
    """生成基于硬件信息的稳定设备ID"""
    try:
        hardware_info = []

        # 1. 获取CPU信息
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId', '/value'],
                                      capture_output=True, text=True, timeout=5)
                for line in result.stdout.split('\n'):
                    if 'ProcessorId=' in line:
                        cpu_id = line.split('=')[1].strip()
                        if cpu_id:
                            hardware_info.append(f"CPU:{cpu_id}")
                            break
            else:
                # Linux/Mac
                result = subprocess.run(['cat', '/proc/cpuinfo'],
                                      capture_output=True, text=True, timeout=5)
                for line in result.stdout.split('\n'):
                    if 'Serial' in line:
                        cpu_id = line.split(':')[1].strip()
                        if cpu_id:
                            hardware_info.append(f"CPU:{cpu_id}")
                            break
        except:
            pass

        # 2. 获取主板序列号
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['wmic', 'baseboard', 'get', 'SerialNumber', '/value'],
                                      capture_output=True, text=True, timeout=5)
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        board_serial = line.split('=')[1].strip()
                        if board_serial and board_serial != 'To be filled by O.E.M.':
                            hardware_info.append(f"BOARD:{board_serial}")
                            break
        except:
            pass

        # 3. 获取MAC地址
        try:
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                           for elements in range(0,2*6,2)][::-1])
            if mac and mac != '00:00:00:00:00:00':
                hardware_info.append(f"MAC:{mac}")
        except:
            pass

        # 4. 获取系统UUID（Windows）
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['wmic', 'csproduct', 'get', 'UUID', '/value'],
                                      capture_output=True, text=True, timeout=5)
                for line in result.stdout.split('\n'):
                    if 'UUID=' in line:
                        system_uuid = line.split('=')[1].strip()
                        if system_uuid and system_uuid != 'To be filled by O.E.M.':
                            hardware_info.append(f"UUID:{system_uuid}")
                            break
        except:
            pass

        # 5. 添加计算机名作为备用标识
        try:
            computer_name = platform.node()
            if computer_name:
                hardware_info.append(f"NAME:{computer_name}")
        except:
            pass

        # 如果没有获取到任何硬件信息，使用随机UUID
        if not hardware_info:
            hardware_info.append(f"RANDOM:{str(uuid.uuid4())}")

        # 生成稳定的设备ID
        combined_info = '|'.join(sorted(hardware_info))
        device_id = hashlib.sha256(combined_info.encode()).hexdigest()[:16].upper()

        logger.info(f"生成设备ID: {device_id} (基于: {len(hardware_info)} 个硬件特征)")
        return device_id

    except Exception as e:
        logger.error(f"生成硬件设备ID异常: {e}")
        # 备用方案：使用随机UUID
        fallback_id = str(uuid.uuid4()).replace('-', '')[:16].upper()
        logger.warning(f"使用备用设备ID: {fallback_id}")
        return fallback_id

class ServerClient(QObject):
    """服务端连接客户端"""
    
    # 信号定义
    connected = pyqtSignal()  # 连接成功
    disconnected = pyqtSignal()  # 连接断开
    photo_settings_received = pyqtSignal(dict)  # 接收到照片参数
    task_received = pyqtSignal(dict)  # 接收到任务
    upload_result = pyqtSignal(bool, str)  # 上传结果
    error_occurred = pyqtSignal(str)  # 错误发生
    station_number_updated = pyqtSignal(int, int)  # 机位号更新 (old_station, new_station)
    task_deleted = pyqtSignal(str)  # 任务删除通知 (task_id)
    test_mode_changed = pyqtSignal(bool, str)  # 测试模式变化信号 (is_test_mode, message)
    
    def __init__(self):
        super().__init__()
        self.socket = None
        self.connected_to_server = False
        self.server_host = None
        self.server_port = config.SERVER_PORT
        self.client_id = None
        self.task_check_thread = None
        self.running = False
        self.photo_settings = None  # 存储服务端照片参数
        # 异步连接状态
        self.connection_pending = False
        self.connection_start_time = 0
        self.ready_for_tasks_pending = False

        # 心跳检测状态
        self.last_heartbeat_sent = 0
        self.last_heartbeat_received = 0
        self.heartbeat_timeout = 60  # 心跳超时时间（秒）
        self.station_number = None  # 存储分配的机位号

        # 任务数量跟踪
        self.current_task_count = 0  # 当前任务数量
        self.last_task_count_report = 0  # 上次报告任务数量的时间
        
    def connect_to_server(self, host: str, port: int = None) -> bool:
        """连接到服务端"""
        try:
            self.server_host = host
            # 如果指定了端口，使用指定的端口，否则使用默认端口
            if port is not None:
                self.server_port = port

            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(config.SERVER_CONNECTION_TIMEOUT)

            # 连接服务端
            self.socket.connect((host, self.server_port))
            
            # 获取机器码（使用简化版本避免超时）
            machine_id = None
            machine_info = None

            try:
                logger.info("开始获取机器码...")
                from core.machine_id import get_simple_machine_id, get_machine_info

                logger.info("正在生成机器码...")
                machine_id = get_simple_machine_id()
                logger.info(f"机器码生成成功: {machine_id}")

                logger.info("正在获取机器信息...")
                machine_info = get_machine_info()
                logger.info("机器信息获取成功")

            except Exception as e:
                logger.error(f"获取机器码失败: {e}")
                # 使用备用机器码
                try:
                    import hashlib
                    timestamp = str(time.time())
                    hostname = socket.gethostname()
                    machine_id = hashlib.md5(f"{timestamp}:{hostname}".encode()).hexdigest()[:12].upper()
                    machine_info = {"error": str(e), "fallback": True}
                    logger.warning(f"使用备用机器码: {machine_id}")
                except Exception as fallback_error:
                    logger.error(f"备用机器码生成也失败: {fallback_error}")
                    machine_id = "FALLBACK123"
                    machine_info = {"error": str(e), "fallback_error": str(fallback_error)}

            # 发送连接消息
            connect_msg = {
                "type": "connect",
                "client_info": {
                    "client_type": "pc_client",
                    "version": config.APP_VERSION,
                    "machine_id": machine_id,
                    "machine_info": machine_info
                }
            }
            
            # 发送连接消息并直接接收响应（简化版本）
            message_str = json.dumps(connect_msg)
            self.socket.send(message_str.encode('utf-8'))
            logger.info("connect消息发送成功")

            # 直接接收响应
            self.socket.settimeout(10.0)
            response_data = self.socket.recv(8192).decode('utf-8')
            response = json.loads(response_data) if response_data else None

            if response and response.get("status") == "success":
                self.connected_to_server = True

                # 生成基于机器码的稳定客户端ID
                if machine_id:
                    # 使用机器码生成稳定的客户端ID
                    self.client_id = f"pc_{machine_id}"
                    logger.info(f"使用机器码生成客户端ID: {self.client_id}")
                else:
                    # 备用方案：使用IP和时间戳
                    try:
                        local_ip = socket.gethostbyname(socket.gethostname())
                    except:
                        local_ip = "unknown"
                    timestamp = int(time.time())
                    self.client_id = f"pc_{local_ip.replace('.', '_')}_{timestamp}"
                    logger.warning(f"机器码不可用，使用备用客户端ID: {self.client_id}")

                # 处理照片参数
                photo_settings = response.get("photo_settings")
                if photo_settings:
                    self.photo_settings = photo_settings
                    self.photo_settings_received.emit(photo_settings)
                    logger.info("已接收服务端照片参数设置")

                # 处理机位号
                station_number = response.get("station_number")
                if station_number:
                    self.station_number = station_number
                    logger.info(f"已分配到 {station_number} 号机位")

                # 初始化心跳时间
                current_time = time.time()
                self.last_heartbeat_sent = 0
                self.last_heartbeat_received = current_time

                # 启动任务检查循环
                logger.info("启动任务检查循环...")
                self.start_task_checking()

                # 发送ready_for_tasks消息
                logger.info("发送ready_for_tasks消息...")
                self._send_ready_for_tasks()

                self.connected.emit()
                logger.info(f"成功连接到服务端: {host}:{self.server_port}")
                return True
            else:
                logger.error("连接失败或响应无效")
                self.error_occurred.emit("服务端连接失败: 连接失败")
                return False
                
        except Exception as e:
            # 连接失败时清理socket
            if hasattr(self, 'socket') and self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None

            self.connected_to_server = False
            self.error_occurred.emit(f"连接服务端异常: {str(e)}")
            logger.error(f"连接服务端异常: {e}")
            return False
    
    def disconnect_from_server(self):
        """断开服务端连接"""
        try:
            # 发送主动断开消息
            if hasattr(self, 'socket') and self.socket and self.connected_to_server:
                try:
                    disconnect_msg = {
                        "type": "disconnect",
                        "client_id": getattr(self, 'client_id', 'unknown'),
                        "reason": "client_shutdown"
                    }
                    message_str = json.dumps(disconnect_msg)
                    self.socket.send(message_str.encode('utf-8'))
                    logger.info("已发送主动断开消息到服务器")

                    # 等待服务器响应（短暂等待）
                    self.socket.settimeout(2.0)
                    try:
                        response_data = self.socket.recv(1024).decode('utf-8')
                        if response_data:
                            response = json.loads(response_data)
                            logger.info(f"服务器确认断开: {response.get('message')}")
                    except:
                        pass  # 忽略响应接收失败
                except Exception as e:
                    logger.warning(f"发送主动断开消息失败: {e}")

            self.running = False
            self.connected_to_server = False
            self.photo_settings = None
            self.station_number = None

            if hasattr(self, 'socket') and self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None

            if hasattr(self, 'sync_socket') and self.sync_socket:
                try:
                    self.sync_socket.close()
                except:
                    pass
                self.sync_socket = None
                
            if self.task_check_thread and self.task_check_thread.is_alive():
                self.task_check_thread.join(timeout=2)
                
            self.disconnected.emit()
            logger.info("已断开服务端连接")
            
        except Exception as e:
            logger.error(f"断开服务端连接异常: {e}")
    
    def start_task_checking(self):
        """启动任务检查"""
        self.running = True
        self.task_check_thread = threading.Thread(target=self._task_check_loop, daemon=True)
        self.task_check_thread.start()

    def _send_ready_for_tasks(self):
        """发送准备接收任务的消息"""
        try:
            ready_msg = {
                "type": "ready_for_tasks",
                "client_id": self.client_id,
                "timestamp": time.time()
            }

            # 异步发送消息
            message_str = json.dumps(ready_msg)
            self.socket.send(message_str.encode('utf-8'))
            self.ready_for_tasks_pending = True
            logger.info("已发送ready_for_tasks消息到服务器")
            return True

        except Exception as e:
            logger.error(f"发送准备接收任务消息异常: {e}")
            return False

    def report_task_count(self, task_count: int):
        """向服务端报告当前任务数量"""
        try:
            if not self.connected_to_server or not self.socket:
                return False

            self.current_task_count = task_count

            task_count_msg = {
                "type": "client_task_count",
                "client_id": self.client_id,
                "task_count": task_count,
                "timestamp": time.time()
            }

            # 异步发送消息
            message_str = json.dumps(task_count_msg)
            self.socket.send(message_str.encode('utf-8'))
            self.last_task_count_report = time.time()
            logger.debug(f"已向服务器报告任务数量: {task_count}")
            return True

        except Exception as e:
            logger.error(f"报告任务数量异常: {e}")
            return False

    def complete_test_task(self, task_id: str, test_data: dict = None):
        """完成测试任务"""
        try:
            if not self.connected_to_server or not self.socket:
                return False

            test_complete_msg = {
                "type": "test_task_complete",
                "task_id": task_id,
                "test_data": test_data or {},
                "timestamp": time.time()
            }

            # 异步发送消息
            message_str = json.dumps(test_complete_msg)
            self.socket.send(message_str.encode('utf-8'))
            logger.info(f"已发送测试任务完成: {task_id}")
            return True

        except Exception as e:
            logger.error(f"完成测试任务异常: {e}")
            return False

    def send_heartbeat(self):
        """发送心跳包（异步方式）"""
        try:
            if not self.connected_to_server or not self.socket:
                return False

            heartbeat_msg = {
                "type": "heartbeat",
                "client_id": self.client_id,
                "timestamp": time.time()
            }

            # 异步发送心跳包，不等待响应
            message_str = json.dumps(heartbeat_msg)
            self.socket.send(message_str.encode('utf-8'))

            # 记录心跳发送时间，用于超时检测
            self.last_heartbeat_sent = time.time()
            logger.debug("心跳包发送成功")
            return True

        except Exception as e:
            logger.error(f"发送心跳包失败: {e}")
            return False
    
    def _task_check_loop(self):
        """消息接收循环"""
        try:
            logger.info("任务检查循环已启动")
            heartbeat_interval = 30  # 每30秒发送一次心跳
            last_heartbeat_time = time.time()

            while self.running and self.connected_to_server:
                try:
                    # 接收服务端消息
                    if self.socket:
                        self.socket.settimeout(config.TASK_CHECK_INTERVAL)
                        try:
                            data = self.socket.recv(8192).decode('utf-8')
                            if data:
                                # 处理可能包含多个JSON消息的数据
                                self._parse_and_handle_messages(data)
                            else:
                                # 服务端关闭了连接
                                logger.warning("服务端关闭了连接")
                                self._handle_server_disconnect()
                                break
                        except socket.timeout:
                            # 超时是正常的，继续循环
                            pass
                        except (ConnectionResetError, ConnectionAbortedError, OSError) as e:
                            logger.warning(f"连接异常: {e}")
                            self._handle_server_disconnect()
                            break
                        except json.JSONDecodeError:
                            logger.warning("收到无效的JSON消息")
                            continue
                    else:
                        time.sleep(config.TASK_CHECK_INTERVAL)

                    # 定期发送心跳包
                    current_time = time.time()
                    time_since_last = current_time - last_heartbeat_time
                    if time_since_last >= heartbeat_interval:
                        last_heartbeat_time = current_time
                        logger.info(f"发送心跳包，距离上次 {time_since_last:.1f} 秒")
                        if not self.send_heartbeat():
                            logger.warning("心跳包发送失败，服务端可能已断开")
                            self._handle_server_disconnect()
                            break

                    # 检查心跳超时
                    if self.last_heartbeat_sent > 0:
                        time_since_last_heartbeat = time.time() - self.last_heartbeat_sent
                        if time_since_last_heartbeat > self.heartbeat_timeout:
                            # 检查是否收到了心跳响应
                            if self.last_heartbeat_received < self.last_heartbeat_sent:
                                logger.warning(f"心跳超时 ({time_since_last_heartbeat:.1f}秒)，服务端可能已断开")
                                self._handle_server_disconnect()
                                break

                except Exception as e:
                    if self.running and self.connected_to_server:
                        logger.error(f"消息接收异常: {e}")
                        # 检查是否是连接相关的异常
                        if isinstance(e, (ConnectionResetError, ConnectionAbortedError, OSError)):
                            logger.warning("检测到连接异常，断开服务器连接")
                            self._handle_server_disconnect()
                            break
                    time.sleep(1)

        except Exception as e:
            logger.error(f"任务检查循环异常: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
        finally:
            logger.info("任务检查循环已结束")

    def _parse_and_handle_messages(self, data: str):
        """解析并处理可能包含多个JSON消息的数据"""
        try:
            # 尝试直接解析单个JSON
            try:
                message = json.loads(data)
                self._handle_server_message(message)
                return
            except json.JSONDecodeError:
                # 可能包含多个JSON消息，需要分割处理
                pass

            # 处理多个JSON消息的情况
            # 简单的方法：按 '}' 分割，然后重新组合
            messages = []
            current_message = ""
            brace_count = 0

            for char in data:
                current_message += char
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        # 完整的JSON消息
                        try:
                            message = json.loads(current_message.strip())
                            messages.append(message)
                            current_message = ""
                        except json.JSONDecodeError:
                            # 忽略无效的JSON片段
                            current_message = ""

            # 处理所有解析出的消息
            for message in messages:
                self._handle_server_message(message)

            if len(messages) > 1:
                logger.info(f"一次接收到 {len(messages)} 个消息")

        except Exception as e:
            logger.error(f"解析多消息数据异常: {e}")
            # 尝试作为单个JSON处理
            try:
                message = json.loads(data)
                self._handle_server_message(message)
            except:
                logger.error(f"无法解析消息数据: {data[:100]}...")

    def _handle_server_disconnect(self):
        """处理服务端断开连接"""
        try:
            logger.info("处理服务端断开连接...")

            # 设置连接状态为False
            self.connected_to_server = False
            self.running = False

            # 清理socket
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None



            # 清理其他状态
            self.client_id = None
            self.photo_settings = None
            self.station_number = None

            # 发出断开连接信号
            self.disconnected.emit()
            self.error_occurred.emit("服务端主动断开了连接")

            logger.info("服务端连接已断开")

        except Exception as e:
            logger.error(f"处理服务端断开连接时出错: {e}")

    def _handle_server_message(self, message):
        """处理服务端消息"""
        try:
            msg_type = message.get("type")



            # 处理ready_for_tasks的响应
            if self.ready_for_tasks_pending and message.get("status") == "success" and "Ready for tasks acknowledged" in message.get("message", ""):
                logger.info("服务器确认已准备接收任务")
                self.ready_for_tasks_pending = False
                return

            # 处理get_task的响应
            if message.get("status") == "success" and "task" in message:
                task = message.get("task")
                if task:
                    logger.info(f"收到get_task响应，任务: {task.get('task_id')}")
                    self.task_received.emit(task)
                return
            elif message.get("status") == "no_task":
                logger.info("服务器响应：没有可分配的任务")
                return

            # 处理心跳响应
            if message.get("status") == "success" and message.get("message") == "heartbeat_ok":
                self.last_heartbeat_received = time.time()
                logger.debug("收到心跳响应")
                return

            if msg_type == "task_assigned":
                # 收到任务分配
                task = message.get("task")
                if task:
                    self.task_received.emit(task)
            elif msg_type == "photo_settings_updated":
                # 收到照片参数更新
                photo_settings = message.get("photo_settings")
                if photo_settings:
                    self.photo_settings = photo_settings
                    self.photo_settings_received.emit(photo_settings)
            elif msg_type == "station_number_update":
                # 收到机位号更新
                new_station_number = message.get("station_number")
                if new_station_number:
                    old_station = self.station_number
                    self.station_number = new_station_number
                    self.station_number_updated.emit(old_station, new_station_number)
                    logger.info(f"机位号已更新: {old_station} -> {new_station_number}")
            elif msg_type == "upload_result":
                # 收到上传结果
                success = message.get("success", False)
                result_message = message.get("message", "")
                self.upload_result.emit(success, result_message)
                logger.info(f"收到上传结果: {success}, {result_message}")
            elif msg_type == "task_deleted":
                # 收到任务删除通知
                task_id = message.get("task_id")
                if task_id:
                    self.task_deleted.emit(task_id)
                    logger.info(f"收到任务删除通知: {task_id}")
            elif msg_type == "test_mode_changed":
                # 收到测试模式变化通知
                is_test_mode = message.get("is_test_mode", False)
                mode_message = message.get("message", "")
                self.test_mode_changed.emit(is_test_mode, mode_message)
                logger.info(f"收到测试模式变化通知: {mode_message}")
            else:
                logger.info(f"收到未知消息类型: {msg_type}")

        except Exception as e:
            logger.error(f"处理服务端消息异常: {e}")

    def request_task(self):
        """主动请求任务"""
        try:
            task_msg = {"type": "get_task"}
            # 直接发送消息，响应会通过_task_check_loop接收
            message_str = json.dumps(task_msg)
            self.socket.send(message_str.encode('utf-8'))
            logger.info("已发送get_task请求到服务器")
            return True

        except Exception as e:
            logger.error(f"请求任务异常: {e}")
            return False

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            cancel_msg = {
                "type": "cancel_task",
                "task_id": task_id
            }
            response = self._send_message(cancel_msg)

            if response and response.get("status") == "success":
                logger.info(f"任务取消成功: {task_id}")
                return True
            else:
                error_msg = response.get("message", "取消任务失败") if response else "无响应"
                logger.warning(f"取消任务失败: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"取消任务异常: {e}")
            return False

    def upload_photo(self, task_id: str, photo_data: bytes, photo_filename: str = None) -> bool:
        """上传照片"""
        try:
            import base64

            # 生成文件名（如果没有提供）
            if not photo_filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                photo_filename = f"id_photo_{task_id}_{timestamp}.jpg"

            # 编码照片数据
            photo_base64 = base64.b64encode(photo_data).decode('utf-8')

            upload_msg = {
                "type": "upload_photo",
                "task_id": task_id,
                "photo_data": photo_base64,
                "photo_filename": photo_filename
            }
            
            # 发送上传消息
            response = self._send_message(upload_msg)
            logger.info(f"上传照片消息发送结果: {response}")

            if response and response.get("status") == "success":
                logger.info("照片上传消息已发送，等待服务端处理结果...")
                # 不在这里发送结果信号，等待服务端的响应消息
                return True
            else:
                error_msg = response.get("message", "发送失败") if response else "发送失败"
                logger.error(f"照片上传消息发送失败: {error_msg}")
                self.upload_result.emit(False, error_msg)
                return False
                
        except Exception as e:
            error_msg = f"上传照片异常: {str(e)}"
            self.upload_result.emit(False, error_msg)
            logger.error(error_msg)
            return False
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            cancel_msg = {
                "type": "cancel_task",
                "task_id": task_id
            }
            
            response = self._send_message(cancel_msg)
            
            if response and response.get("status") == "success":
                logger.info(f"取消任务消息已发送: {task_id}")
                return True
            else:
                error_msg = response.get("message", "发送取消消息失败") if response else "发送取消消息失败"
                logger.warning(f"发送取消任务消息失败: {error_msg}")
                self.error_occurred.emit(error_msg)
                return False
                
        except Exception as e:
            error_msg = f"取消任务异常: {str(e)}"
            self.error_occurred.emit(error_msg)
            logger.error(error_msg)
            return False
    
    def _send_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送消息并接收响应（简化版本）"""
        try:
            if not self.socket:
                logger.error("Socket未连接")
                return {"status": "error", "message": "未连接到服务端"}

            # 发送消息
            try:
                message_str = json.dumps(message)
                self.socket.send(message_str.encode('utf-8'))
                logger.info(f"已发送消息，类型: {message.get('type')}")

                # 简单等待，让服务端有时间处理
                time.sleep(0.5)

                # 返回成功状态，让调用方通过信号获取实际结果
                return {"status": "success", "message": "消息已发送"}

            except Exception as send_error:
                logger.error(f"发送消息失败: {send_error}")
                return {"status": "error", "message": f"发送失败: {str(send_error)}"}

        except Exception as e:
            logger.error(f"发送消息异常: {e}")
            return {"status": "error", "message": f"发送消息异常: {str(e)}"}



    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connected_to_server and self.socket is not None

    def get_station_number(self) -> int:
        """获取分配的机位号"""
        return self.station_number if self.station_number else 0

    def get_server_info(self) -> Dict[str, Any]:
        """获取服务端信息"""
        return {
            "host": self.server_host,
            "port": self.server_port,
            "connected": self.connected_to_server,
            "client_id": self.client_id,
            "station_number": self.station_number
        }
