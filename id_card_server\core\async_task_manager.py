#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步任务管理器
使用异步通信防止冲突和阻塞
"""

import asyncio
import json
import time
import logging
import threading
import queue
from typing import Dict, Any, Optional, Set
from PyQt5.QtCore import QObject, pyqtSignal

logger = logging.getLogger(__name__)

class AsyncTaskManager(QObject):
    """异步任务管理器"""
    
    # 信号定义
    log_message = pyqtSignal(str)
    client_connected = pyqtSignal(str, str)
    client_disconnected = pyqtSignal(str, str)
    task_assigned = pyqtSignal(str, str)
    photo_uploaded = pyqtSignal(str, str)
    
    def __init__(self, db, port: int = 9090):
        super().__init__()
        self.db = db
        self.port = port
        self.server = None
        self.running = False
        
        # 客户端连接管理
        self.connected_clients: Dict[str, Dict] = {}
        self.client_writers: Dict[str, asyncio.StreamWriter] = {}
        
        # 消息队列
        self.message_queue = queue.Queue()
        self.response_queue = queue.Queue()
        
        # 任务来源
        self.task_source = "photo_tasks"
        
        # 照片参数
        self.photo_settings = {
            "size": {"name": "自定义", "width": 450, "height": 600},
            "background_color": {"name": "白色", "value": "#FFFFFF"}
        }
        
        # 异步事件循环
        self.loop = None
        self.loop_thread = None
        
    def start_server(self):
        """启动异步服务器"""
        try:
            self.running = True
            
            # 在单独线程中运行异步事件循环
            self.loop_thread = threading.Thread(target=self._run_async_server, daemon=True)
            self.loop_thread.start()
            
            logger.info(f"异步任务管理器已启动，端口: {self.port}")
            return True
            
        except Exception as e:
            logger.error(f"启动异步服务器失败: {e}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        try:
            self.running = False
            
            if self.loop and self.loop.is_running():
                # 在事件循环中安排停止任务
                asyncio.run_coroutine_threadsafe(self._stop_async_server(), self.loop)
            
            if self.loop_thread:
                self.loop_thread.join(timeout=5)
            
            logger.info("异步任务管理器已停止")
            
        except Exception as e:
            logger.error(f"停止异步服务器异常: {e}")
    
    def _run_async_server(self):
        """运行异步服务器"""
        try:
            # 创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            # 启动服务器
            self.loop.run_until_complete(self._start_async_server())
            
        except Exception as e:
            logger.error(f"异步服务器运行异常: {e}")
    
    async def _start_async_server(self):
        """启动异步服务器协程"""
        try:
            self.server = await asyncio.start_server(
                self._handle_client_connection,
                '0.0.0.0',
                self.port
            )
            
            logger.info(f"异步服务器已启动，监听端口 {self.port}")
            
            # 启动任务分配协程
            asyncio.create_task(self._task_assignment_loop())
            
            # 启动心跳检查协程
            asyncio.create_task(self._heartbeat_check_loop())
            
            async with self.server:
                await self.server.serve_forever()
                
        except Exception as e:
            logger.error(f"启动异步服务器协程异常: {e}")
    
    async def _stop_async_server(self):
        """停止异步服务器协程"""
        try:
            if self.server:
                self.server.close()
                await self.server.wait_closed()
            
            # 关闭所有客户端连接
            for client_id, writer in self.client_writers.items():
                try:
                    writer.close()
                    await writer.wait_closed()
                except:
                    pass
            
            self.client_writers.clear()
            self.connected_clients.clear()
            
        except Exception as e:
            logger.error(f"停止异步服务器协程异常: {e}")
    
    async def _handle_client_connection(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """处理客户端连接"""
        client_address = writer.get_extra_info('peername')
        client_id = f"client_{client_address[0]}_{client_address[1]}_{int(time.time())}"
        
        try:
            logger.info(f"新客户端连接: {client_address} (ID: {client_id})")
            
            # 存储客户端信息
            self.connected_clients[client_id] = {
                "address": client_address,
                "connected_time": time.time(),
                "last_heartbeat": time.time(),
                "machine_id": None,
                "station_number": 1,
                "ready_for_tasks": False
            }
            
            self.client_writers[client_id] = writer
            
            # 发送连接成功信号
            self.client_connected.emit(client_id, f"{client_address[0]}:{client_address[1]}")
            
            # 处理客户端消息
            await self._handle_client_messages(client_id, reader, writer)
            
        except Exception as e:
            logger.error(f"处理客户端连接异常: {e}")
        finally:
            # 清理客户端连接
            await self._cleanup_client(client_id)
    
    async def _handle_client_messages(self, client_id: str, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """处理客户端消息"""
        try:
            while self.running:
                try:
                    # 异步接收消息
                    data = await asyncio.wait_for(reader.read(8192), timeout=30.0)
                    
                    if not data:
                        logger.info(f"客户端 {client_id} 断开连接")
                        break
                    
                    message_str = data.decode('utf-8')
                    
                    # 解析并处理消息
                    await self._process_client_message(client_id, message_str, writer)
                    
                except asyncio.TimeoutError:
                    # 检查心跳超时
                    if self._is_heartbeat_timeout(client_id):
                        logger.warning(f"客户端 {client_id} 心跳超时")
                        break
                    continue
                    
                except Exception as e:
                    logger.error(f"处理客户端 {client_id} 消息异常: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"客户端消息处理循环异常: {e}")
    
    async def _process_client_message(self, client_id: str, message_str: str, writer: asyncio.StreamWriter):
        """处理客户端消息"""
        try:
            message = json.loads(message_str)
            msg_type = message.get('type', 'unknown')
            
            logger.info(f"收到客户端 {client_id} 消息: {msg_type}")
            
            # 更新心跳时间
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['last_heartbeat'] = time.time()
            
            # 处理不同类型的消息
            response = await self._handle_message_type(client_id, message)
            
            # 异步发送响应
            if response:
                await self._send_response(writer, response)
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            error_response = {"status": "error", "message": "Invalid JSON"}
            await self._send_response(writer, error_response)
            
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
            error_response = {"status": "error", "message": str(e)}
            await self._send_response(writer, error_response)
    
    async def _handle_message_type(self, client_id: str, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理不同类型的消息"""
        msg_type = message.get('type')
        
        if msg_type == 'connect':
            return await self._handle_connect(client_id, message)
        elif msg_type == 'ready_for_tasks':
            return await self._handle_ready_for_tasks(client_id, message)
        elif msg_type == 'upload_photo':
            return await self._handle_photo_upload(client_id, message)
        elif msg_type == 'test_task_complete':
            return await self._handle_test_task_complete(client_id, message)
        elif msg_type == 'heartbeat':
            return {"status": "success", "message": "heartbeat_ack"}
        else:
            return {"status": "error", "message": f"Unknown message type: {msg_type}"}
    
    async def _handle_connect(self, client_id: str, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理连接消息"""
        try:
            client_info = message.get('client_info', {})
            machine_id = client_info.get('machine_id', '')
            
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['machine_id'] = machine_id
                self.connected_clients[client_id]['machine_info'] = client_info
                
                # 分配机位号
                station_number = self._assign_station_number(machine_id)
                self.connected_clients[client_id]['station_number'] = station_number
            
            return {
                "status": "success",
                "message": "Connected to server",
                "photo_settings": self.photo_settings,
                "station_number": station_number
            }
            
        except Exception as e:
            logger.error(f"处理连接消息异常: {e}")
            return {"status": "error", "message": str(e)}
    
    async def _handle_ready_for_tasks(self, client_id: str, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理准备接收任务消息"""
        try:
            if client_id in self.connected_clients:
                self.connected_clients[client_id]['ready_for_tasks'] = True
                
                # 异步发送测试模式状态
                await self._send_test_mode_status(client_id)
            
            return {"status": "success", "message": "Ready for tasks"}
            
        except Exception as e:
            logger.error(f"处理准备接收任务消息异常: {e}")
            return {"status": "error", "message": str(e)}
    
    async def _send_response(self, writer: asyncio.StreamWriter, response: Dict[str, Any]):
        """异步发送响应"""
        try:
            response_str = json.dumps(response)
            writer.write(response_str.encode('utf-8'))
            await writer.drain()
            
        except Exception as e:
            logger.error(f"发送响应异常: {e}")
    
    async def _send_test_mode_status(self, client_id: str):
        """发送测试模式状态"""
        try:
            if client_id not in self.client_writers:
                return
            
            test_mode_message = {
                "type": "test_mode_changed",
                "is_test_mode": self.task_source == "test_tasks",
                "message": "测试模式" if self.task_source == "test_tasks" else "正式模式"
            }
            
            writer = self.client_writers[client_id]
            await self._send_response(writer, test_mode_message)
            
        except Exception as e:
            logger.error(f"发送测试模式状态异常: {e}")
    
    def _assign_station_number(self, machine_id: str) -> int:
        """分配机位号"""
        # 简单的机位号分配逻辑
        return len(self.connected_clients) % 10 + 1
    
    def _is_heartbeat_timeout(self, client_id: str) -> bool:
        """检查心跳超时"""
        if client_id not in self.connected_clients:
            return True
        
        last_heartbeat = self.connected_clients[client_id]['last_heartbeat']
        return time.time() - last_heartbeat > 60  # 60秒超时
    
    async def _cleanup_client(self, client_id: str):
        """清理客户端连接"""
        try:
            if client_id in self.client_writers:
                writer = self.client_writers[client_id]
                try:
                    writer.close()
                    await writer.wait_closed()
                except:
                    pass
                del self.client_writers[client_id]
            
            if client_id in self.connected_clients:
                address = self.connected_clients[client_id]['address']
                del self.connected_clients[client_id]
                self.client_disconnected.emit(client_id, f"{address[0]}:{address[1]}")
            
            logger.info(f"客户端 {client_id} 已清理")
            
        except Exception as e:
            logger.error(f"清理客户端异常: {e}")
    
    async def _task_assignment_loop(self):
        """任务分配循环"""
        while self.running:
            try:
                await asyncio.sleep(1)  # 每秒检查一次
                await self._assign_tasks_to_clients()
                
            except Exception as e:
                logger.error(f"任务分配循环异常: {e}")
                await asyncio.sleep(5)
    
    async def _assign_tasks_to_clients(self):
        """为客户端分配任务"""
        try:
            # 获取准备就绪的客户端
            ready_clients = [
                client_id for client_id, info in self.connected_clients.items()
                if info.get('ready_for_tasks', False)
            ]
            
            if not ready_clients:
                return
            
            # 这里可以添加具体的任务分配逻辑
            # 暂时跳过，保持与原有逻辑兼容
            
        except Exception as e:
            logger.error(f"分配任务异常: {e}")
    
    async def _heartbeat_check_loop(self):
        """心跳检查循环"""
        while self.running:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次
                
                # 检查所有客户端的心跳
                timeout_clients = []
                for client_id in list(self.connected_clients.keys()):
                    if self._is_heartbeat_timeout(client_id):
                        timeout_clients.append(client_id)
                
                # 清理超时客户端
                for client_id in timeout_clients:
                    logger.warning(f"客户端 {client_id} 心跳超时，断开连接")
                    await self._cleanup_client(client_id)
                
            except Exception as e:
                logger.error(f"心跳检查循环异常: {e}")
    
    # 其他方法的异步版本...
    async def _handle_photo_upload(self, client_id: str, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理照片上传"""
        # 实现照片上传逻辑
        return {"status": "success", "message": "Photo uploaded"}
    
    async def _handle_test_task_complete(self, client_id: str, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理测试任务完成"""
        # 实现测试任务完成逻辑
        return {"status": "success", "message": "Test task completed"}
    
    def set_task_source(self, task_source: str):
        """设置任务来源"""
        self.task_source = task_source

        # 异步广播测试模式变化
        if self.loop and self.loop.is_running():
            asyncio.run_coroutine_threadsafe(self._broadcast_test_mode_change(), self.loop)

    async def _broadcast_test_mode_change(self):
        """广播测试模式变化"""
        try:
            is_test_mode = self.task_source == "test_tasks"
            test_mode_message = {
                "type": "test_mode_changed",
                "is_test_mode": is_test_mode,
                "message": "测试模式" if is_test_mode else "正式模式"
            }

            # 向所有连接的客户端发送消息
            for client_id, writer in self.client_writers.items():
                try:
                    await self._send_response(writer, test_mode_message)
                    logger.info(f"已向客户端 {client_id} 发送测试模式变化通知")
                except Exception as e:
                    logger.error(f"向客户端 {client_id} 发送测试模式变化失败: {e}")

        except Exception as e:
            logger.error(f"广播测试模式变化异常: {e}")

    async def send_task_to_client(self, client_id: str, task: Dict[str, Any]):
        """向客户端发送任务"""
        try:
            if client_id not in self.client_writers:
                return False

            task_message = {
                "type": "task_assigned",
                "task": task
            }

            writer = self.client_writers[client_id]
            await self._send_response(writer, task_message)

            self.task_assigned.emit(task.get('task_id', ''), client_id)
            return True

        except Exception as e:
            logger.error(f"向客户端 {client_id} 发送任务异常: {e}")
            return False

    def get_connected_clients_info(self) -> Dict[str, Dict]:
        """获取连接的客户端信息"""
        return self.connected_clients.copy()

    def is_client_connected(self, client_id: str) -> bool:
        """检查客户端是否连接"""
        return client_id in self.connected_clients
