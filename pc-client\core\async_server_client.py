#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步服务器客户端
使用异步通信防止冲突和阻塞
"""

import asyncio
import json
import time
import logging
import threading
import queue
from typing import Dict, Any, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal
import config

logger = logging.getLogger(__name__)

class AsyncServerClient(QObject):
    """异步服务器客户端"""
    
    # 信号定义
    connected = pyqtSignal()
    disconnected = pyqtSignal()
    photo_settings_received = pyqtSignal(dict)
    task_received = pyqtSignal(dict)
    upload_result = pyqtSignal(bool, str)
    error_occurred = pyqtSignal(str)
    station_number_updated = pyqtSignal(int, int)
    task_deleted = pyqtSignal(str)
    test_mode_changed = pyqtSignal(bool, str)
    
    def __init__(self):
        super().__init__()
        
        # 连接状态
        self.connected_to_server = False
        self.server_host = 'localhost'
        self.server_port = config.SERVER_PORT
        
        # 异步组件
        self.reader: Optional[asyncio.StreamReader] = None
        self.writer: Optional[asyncio.StreamWriter] = None
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.loop_thread: Optional[threading.Thread] = None
        self.running = False
        
        # 客户端信息
        self.client_id = ""
        self.machine_id = ""
        self.station_number = 0
        self.photo_settings = {}
        
        # 消息队列
        self.send_queue = queue.Queue()
        self.response_callbacks: Dict[str, Callable] = {}
        
        # 心跳
        self.last_heartbeat_sent = 0
        self.last_heartbeat_received = 0
        
    def connect_to_server(self, host: str, port: int = None) -> bool:
        """连接到服务器"""
        try:
            self.server_host = host
            if port is not None:
                self.server_port = port
            
            # 启动异步事件循环
            self.running = True
            self.loop_thread = threading.Thread(target=self._run_async_client, daemon=True)
            self.loop_thread.start()
            
            # 等待连接结果
            start_time = time.time()
            while time.time() - start_time < 10:  # 10秒超时
                if self.connected_to_server:
                    return True
                time.sleep(0.1)
            
            logger.error("连接超时")
            return False
            
        except Exception as e:
            logger.error(f"连接服务器异常: {e}")
            return False
    
    def disconnect_from_server(self):
        """断开服务器连接"""
        try:
            self.running = False
            
            if self.loop and self.loop.is_running():
                # 在事件循环中安排断开任务
                asyncio.run_coroutine_threadsafe(self._disconnect_async(), self.loop)
            
            if self.loop_thread:
                self.loop_thread.join(timeout=5)
            
            self.connected_to_server = False
            self.disconnected.emit()
            
        except Exception as e:
            logger.error(f"断开服务器连接异常: {e}")
    
    def _run_async_client(self):
        """运行异步客户端"""
        try:
            # 创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            # 运行连接协程
            self.loop.run_until_complete(self._connect_and_run())
            
        except Exception as e:
            logger.error(f"异步客户端运行异常: {e}")
        finally:
            if self.connected_to_server:
                self.connected_to_server = False
                self.disconnected.emit()
    
    async def _connect_and_run(self):
        """连接并运行"""
        try:
            # 建立连接
            self.reader, self.writer = await asyncio.open_connection(
                self.server_host, self.server_port
            )
            
            logger.info(f"已连接到服务器 {self.server_host}:{self.server_port}")
            
            # 发送连接消息
            await self._send_connect_message()
            
            # 启动消息处理任务
            receive_task = asyncio.create_task(self._receive_messages())
            send_task = asyncio.create_task(self._send_messages())
            heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            # 等待任务完成
            await asyncio.gather(receive_task, send_task, heartbeat_task, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"连接和运行异常: {e}")
            self.error_occurred.emit(f"连接异常: {str(e)}")
        finally:
            await self._disconnect_async()
    
    async def _send_connect_message(self):
        """发送连接消息"""
        try:
            # 生成机器码
            from core.machine_id import get_simple_machine_id, get_machine_info
            
            self.machine_id = get_simple_machine_id()
            machine_info = get_machine_info()
            
            connect_msg = {
                "type": "connect",
                "client_info": {
                    "client_type": "pc_client",
                    "version": config.APP_VERSION,
                    "machine_id": self.machine_id,
                    "machine_info": machine_info
                }
            }
            
            # 发送连接消息
            await self._send_message(connect_msg)
            
            # 接收连接响应
            response_data = await asyncio.wait_for(self.reader.read(8192), timeout=10.0)
            response = json.loads(response_data.decode('utf-8'))
            
            if response.get('status') == 'success':
                self.connected_to_server = True
                self.client_id = f"pc_{self.machine_id}"
                
                # 处理连接响应
                self.photo_settings = response.get('photo_settings', {})
                self.station_number = response.get('station_number', 0)
                
                # 发送信号
                self.connected.emit()
                if self.photo_settings:
                    self.photo_settings_received.emit(self.photo_settings)
                
                # 发送ready消息
                await self._send_ready_message()
                
                logger.info("连接成功，已发送ready消息")
            else:
                raise Exception(f"连接失败: {response}")
                
        except Exception as e:
            logger.error(f"发送连接消息异常: {e}")
            raise
    
    async def _send_ready_message(self):
        """发送准备消息"""
        try:
            ready_msg = {
                "type": "ready_for_tasks",
                "client_id": self.client_id,
                "timestamp": time.time()
            }
            
            await self._send_message(ready_msg)
            
        except Exception as e:
            logger.error(f"发送准备消息异常: {e}")
    
    async def _receive_messages(self):
        """接收消息循环"""
        try:
            while self.running and self.connected_to_server:
                try:
                    # 异步接收消息
                    data = await asyncio.wait_for(self.reader.read(8192), timeout=5.0)
                    
                    if not data:
                        logger.warning("服务器关闭了连接")
                        break
                    
                    message_str = data.decode('utf-8')
                    await self._process_received_message(message_str)
                    
                except asyncio.TimeoutError:
                    # 检查心跳超时
                    if time.time() - self.last_heartbeat_received > 90:  # 90秒超时
                        logger.warning("心跳超时")
                        break
                    continue
                    
                except Exception as e:
                    logger.error(f"接收消息异常: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"接收消息循环异常: {e}")
    
    async def _process_received_message(self, message_str: str):
        """处理接收到的消息"""
        try:
            # 尝试解析JSON
            try:
                message = json.loads(message_str)
                await self._handle_message(message)
            except json.JSONDecodeError:
                # 尝试按行分割多个消息
                for line in message_str.strip().split('\n'):
                    if line.strip():
                        try:
                            message = json.loads(line.strip())
                            await self._handle_message(message)
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"处理接收消息异常: {e}")
    
    async def _handle_message(self, message: Dict[str, Any]):
        """处理单个消息"""
        try:
            msg_type = message.get('type')
            
            if msg_type == 'task_assigned':
                task = message.get('task')
                if task:
                    self.task_received.emit(task)
                    
            elif msg_type == 'test_mode_changed':
                is_test_mode = message.get('is_test_mode', False)
                mode_message = message.get('message', '')
                self.test_mode_changed.emit(is_test_mode, mode_message)
                
            elif msg_type == 'upload_result':
                success = message.get('success', False)
                result_message = message.get('message', '')
                self.upload_result.emit(success, result_message)
                
            elif msg_type == 'photo_settings_updated':
                photo_settings = message.get('photo_settings')
                if photo_settings:
                    self.photo_settings = photo_settings
                    self.photo_settings_received.emit(photo_settings)
                    
            elif msg_type == 'station_number_update':
                new_station = message.get('station_number')
                if new_station:
                    old_station = self.station_number
                    self.station_number = new_station
                    self.station_number_updated.emit(old_station, new_station)
                    
            elif msg_type == 'task_deleted':
                task_id = message.get('task_id')
                if task_id:
                    self.task_deleted.emit(task_id)
                    
            # 更新心跳时间
            self.last_heartbeat_received = time.time()
            
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
    
    async def _send_messages(self):
        """发送消息循环"""
        try:
            while self.running and self.connected_to_server:
                try:
                    # 检查发送队列
                    if not self.send_queue.empty():
                        message = self.send_queue.get_nowait()
                        await self._send_message(message)
                    
                    await asyncio.sleep(0.1)  # 短暂休眠
                    
                except Exception as e:
                    logger.error(f"发送消息异常: {e}")
                    
        except Exception as e:
            logger.error(f"发送消息循环异常: {e}")
    
    async def _send_message(self, message: Dict[str, Any]):
        """发送消息"""
        try:
            if not self.writer:
                return
            
            message_str = json.dumps(message)
            self.writer.write(message_str.encode('utf-8'))
            await self.writer.drain()
            
        except Exception as e:
            logger.error(f"发送消息异常: {e}")
            raise
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        try:
            while self.running and self.connected_to_server:
                try:
                    current_time = time.time()
                    
                    # 每30秒发送心跳
                    if current_time - self.last_heartbeat_sent > 30:
                        heartbeat_msg = {
                            "type": "heartbeat",
                            "timestamp": current_time
                        }
                        
                        await self._send_message(heartbeat_msg)
                        self.last_heartbeat_sent = current_time
                    
                    await asyncio.sleep(10)  # 每10秒检查一次
                    
                except Exception as e:
                    logger.error(f"心跳异常: {e}")
                    
        except Exception as e:
            logger.error(f"心跳循环异常: {e}")
    
    async def _disconnect_async(self):
        """异步断开连接"""
        try:
            if self.writer:
                self.writer.close()
                await self.writer.wait_closed()
            
            self.reader = None
            self.writer = None
            
        except Exception as e:
            logger.error(f"异步断开连接异常: {e}")
    
    # 公共接口方法
    def upload_photo(self, task_id: str, photo_data: bytes, filename: str):
        """上传照片"""
        try:
            import base64
            
            upload_msg = {
                "type": "upload_photo",
                "task_id": task_id,
                "photo_data": base64.b64encode(photo_data).decode('utf-8'),
                "photo_filename": filename
            }
            
            self.send_queue.put(upload_msg)
            
        except Exception as e:
            logger.error(f"上传照片异常: {e}")
            self.error_occurred.emit(f"上传照片失败: {str(e)}")
    
    def complete_test_task(self, task_id: str, test_data: dict = None):
        """完成测试任务"""
        try:
            complete_msg = {
                "type": "test_task_complete",
                "task_id": task_id,
                "test_data": test_data or {},
                "timestamp": time.time()
            }
            
            self.send_queue.put(complete_msg)
            
        except Exception as e:
            logger.error(f"完成测试任务异常: {e}")
            self.error_occurred.emit(f"完成测试任务失败: {str(e)}")
    
    def get_station_number(self) -> int:
        """获取机位号"""
        return self.station_number
