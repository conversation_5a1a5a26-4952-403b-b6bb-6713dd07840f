#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试服务器定时器
"""

import socket
import json
import time

def send_debug_command(command):
    """发送调试命令到服务器"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect(("10.21.7.254", 9090))
        
        # 发送调试消息
        debug_msg = {
            "type": "debug",
            "command": command,
            "timestamp": time.time()
        }
        
        sock.send(json.dumps(debug_msg).encode('utf-8'))
        
        # 接收响应
        response_data = sock.recv(8192).decode('utf-8')
        if response_data:
            response = json.loads(response_data)
            return response
        
        sock.close()
        return None
        
    except Exception as e:
        print(f"❌ 发送调试命令失败: {e}")
        return None

def check_server_timer_status():
    """检查服务器定时器状态"""
    print("=== 检查服务器定时器状态 ===")
    
    # 先连接一个客户端
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        sock.connect(("10.21.7.254", 9090))
        
        # 发送连接消息
        connect_msg = {
            "type": "connect",
            "client_info": {
                "client_type": "pc_client",
                "version": "1.0.0",
                "machine_id": "30206519B208",
                "machine_info": {"system": "Windows"}
            }
        }
        
        sock.send(json.dumps(connect_msg).encode('utf-8'))
        response_data = sock.recv(8192).decode('utf-8')
        response = json.loads(response_data)
        
        if response.get("status") == "success":
            print("✅ 客户端连接成功")
            
            # 发送ready_for_tasks
            ready_msg = {
                "type": "ready_for_tasks",
                "client_id": "pc_30206519B208",
                "timestamp": time.time()
            }
            
            sock.send(json.dumps(ready_msg).encode('utf-8'))
            ready_response_data = sock.recv(8192).decode('utf-8')
            ready_response = json.loads(ready_response_data)
            
            if ready_response.get("status") == "success":
                print("✅ 客户端已准备接收任务")
                
                # 现在创建一个任务并观察
                print("\n=== 创建任务并观察分配过程 ===")
                
                import sqlite3
                import uuid
                
                # 创建测试任务
                db_path = "id_card_server/photo_tasks.db"
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                task_id = f"DEBUG_{uuid.uuid4().hex[:8].upper()}"
                name = f"DEBUG_TEST_{int(time.time())}"
                
                cursor.execute('''
                    INSERT INTO photo_tasks (task_id, name, gender, birth_date, id_number, task_status, created_time)
                    VALUES (?, ?, ?, ?, ?, 'pending', datetime('now'))
                ''', (task_id, name, "男", "1990-01-01", "110101199001010001"))
                
                conn.commit()
                conn.close()
                
                print(f"✅ 创建调试任务: {task_id}")
                
                # 等待并检查任务状态
                print("等待任务分配（30秒）...")
                
                for i in range(30):
                    time.sleep(1)
                    
                    # 检查任务状态
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT task_status, pc_client_id
                        FROM photo_tasks
                        WHERE task_id = ?
                    ''', (task_id,))
                    
                    result = cursor.fetchone()
                    conn.close()
                    
                    if result:
                        status, client_id = result
                        if status != 'pending':
                            print(f"✅ 任务状态变化: {status} (客户端: {client_id})")
                            break
                        elif i % 5 == 0:  # 每5秒输出一次
                            print(f"   第{i+1}秒: 任务仍为pending状态")
                    
                    # 检查是否收到任务
                    try:
                        sock.settimeout(0.1)
                        data = sock.recv(8192).decode('utf-8')
                        if data:
                            message = json.loads(data)
                            if message.get("type") == "task_assigned":
                                task = message.get("task", {})
                                print(f"✅ 收到任务分配: {task.get('name')} (ID: {task.get('task_id')})")
                                if task.get('task_id') == task_id:
                                    print("✅ 收到目标任务！")
                                    break
                    except socket.timeout:
                        pass
                    except:
                        pass
                
                # 清理测试任务
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("DELETE FROM photo_tasks WHERE name LIKE 'DEBUG_TEST_%'")
                conn.commit()
                conn.close()
                
            else:
                print(f"❌ 客户端准备失败: {ready_response}")
        else:
            print(f"❌ 客户端连接失败: {response}")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ 调试过程异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始调试服务器定时器...\n")
    
    check_server_timer_status()
    
    print("\n=== 调试完成 ===")
    print("如果任务没有被自动分配，可能的原因：")
    print("1. 服务器端定时器没有启动")
    print("2. 定时器启动了但check_and_assign_tasks方法有问题")
    print("3. 客户端状态没有正确设置")
    print("4. 数据库查询有问题")
