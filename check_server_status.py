#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查服务器状态
"""

import socket
import json
import time

def check_server_connection():
    """检查服务器连接"""
    try:
        print("=== 检查服务器连接 ===")
        
        # 尝试连接服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect(("10.21.7.254", 9090))
        
        print("✅ 服务器连接成功")
        
        # 发送测试消息
        test_msg = {
            "type": "heartbeat",
            "client_id": "test_client",
            "timestamp": time.time()
        }
        
        sock.send(json.dumps(test_msg).encode('utf-8'))
        
        # 接收响应
        response_data = sock.recv(1024).decode('utf-8')
        if response_data:
            response = json.loads(response_data)
            print(f"✅ 服务器响应: {response.get('status')}")
            print(f"   消息: {response.get('message')}")
        else:
            print("❌ 服务器无响应")
        
        sock.close()
        return True
        
    except ConnectionRefusedError:
        print("❌ 服务器连接被拒绝 - 服务器可能没有运行")
        return False
    except socket.timeout:
        print("❌ 服务器连接超时")
        return False
    except Exception as e:
        print(f"❌ 服务器连接异常: {e}")
        return False

def check_database_tasks():
    """检查数据库中的任务"""
    try:
        print("\n=== 检查数据库任务 ===")
        
        import sqlite3
        
        db_path = "id_card_server/photo_tasks.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查pending任务
        cursor.execute("SELECT COUNT(*) FROM photo_tasks WHERE task_status = 'pending'")
        pending_count = cursor.fetchone()[0]
        
        # 检查assigned任务
        cursor.execute("SELECT COUNT(*) FROM photo_tasks WHERE task_status = 'assigned'")
        assigned_count = cursor.fetchone()[0]
        
        # 检查completed任务
        cursor.execute("SELECT COUNT(*) FROM photo_tasks WHERE task_status = 'completed'")
        completed_count = cursor.fetchone()[0]
        
        print(f"   待分配任务: {pending_count}")
        print(f"   已分配任务: {assigned_count}")
        print(f"   已完成任务: {completed_count}")
        
        # 检查最近的任务
        cursor.execute('''
            SELECT task_id, name, task_status, pc_client_id, created_time
            FROM photo_tasks
            ORDER BY created_time DESC
            LIMIT 5
        ''')
        
        recent_tasks = cursor.fetchall()
        print(f"\n   最近5个任务:")
        for task in recent_tasks:
            task_id, name, status, client_id, created_time = task
            print(f"     {name}: {status} (客户端: {client_id or 'None'}) - {created_time}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查异常: {e}")
        return False

if __name__ == "__main__":
    print("开始检查服务器状态...\n")
    
    # 检查1：服务器连接
    server_ok = check_server_connection()
    
    # 检查2：数据库任务
    db_ok = check_database_tasks()
    
    print(f"\n=== 检查结果 ===")
    print(f"服务器连接: {'✅ 正常' if server_ok else '❌ 异常'}")
    print(f"数据库状态: {'✅ 正常' if db_ok else '❌ 异常'}")
    
    if not server_ok:
        print("\n💡 建议:")
        print("   1. 检查服务器是否已启动")
        print("   2. 检查服务器端口9090是否被占用")
        print("   3. 检查防火墙设置")
    
    if server_ok and db_ok:
        print("\n✅ 服务器状态正常，可以进行任务分配测试")
