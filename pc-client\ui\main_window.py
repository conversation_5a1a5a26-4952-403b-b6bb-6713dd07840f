#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
"""

import sys
import os
import socket
import threading
import time
import logging
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QPushButton, QTextEdit,
                             QGroupBox, QGridLayout, QMessageBox, QProgressBar,
                             QListWidget, QListWidgetItem, QSplitter, QFrame, QFileDialog, QDialog)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor

from core.phone_controller import PhoneController
from core.server_client import ServerClient
from ui.photo_viewer import PhotoViewer
from ui.device_selector import DeviceSelectorDialog
from ui.photo_settings_dialog import PhotoSettingsDialog
from ui.server_selector_dialog import ServerSelectorDialog
import config

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.phone_controller = PhoneController()
        self.server_client = ServerClient()
        self.photo_viewer = PhotoViewer(self.phone_controller, self)
        self.server_mode = False  # 是否为服务端模式
        self.test_mode = False  # 是否为测试模式
        self.current_task = None  # 当前任务
        self.task_queue = []  # 任务队列
        self.processing_task = False  # 是否正在处理任务
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"{config.APP_NAME} - 电脑客户端 v{config.APP_VERSION}")
        self.setGeometry(100, 100, config.WINDOW_WIDTH, config.WINDOW_HEIGHT)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧照片查看器
        splitter.addWidget(self.photo_viewer)
        
        # 设置分割器比例
        splitter.setSizes([400, 800])
        
        # 设置状态栏
        self.statusBar().showMessage("就绪")
        
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 连接设置组
        connection_group = QGroupBox("设备连接")
        connection_layout = QVBoxLayout(connection_group)

        # 设备发现按钮
        self.discover_btn = QPushButton("🔍 发现并连接设备")
        self.discover_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        connection_layout.addWidget(self.discover_btn)

        # 断开连接按钮（初始隐藏）
        self.disconnect_btn = QPushButton("断开连接")
        self.disconnect_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        self.disconnect_btn.setVisible(False)
        connection_layout.addWidget(self.disconnect_btn)
        
        layout.addWidget(connection_group)

        # 服务端连接组
        server_group = QGroupBox("服务端连接")
        server_layout = QVBoxLayout(server_group)

        # 服务端IP输入
        server_ip_layout = QHBoxLayout()
        server_ip_layout.addWidget(QLabel("服务端IP:"))
        self.server_ip_input = QLineEdit()
        self.server_ip_input.setPlaceholderText("输入服务端IP地址")
        self.server_ip_input.setText("127.0.0.1")  # 默认本地
        server_ip_layout.addWidget(self.server_ip_input)
        server_layout.addLayout(server_ip_layout)

        # 连接服务端按钮
        self.connect_server_btn = QPushButton("🔗 选择服务端")
        self.connect_server_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        server_layout.addWidget(self.connect_server_btn)

        # 断开服务端按钮（初始隐藏）
        self.disconnect_server_btn = QPushButton("断开服务端")
        self.disconnect_server_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        self.disconnect_server_btn.setVisible(False)
        server_layout.addWidget(self.disconnect_server_btn)

        layout.addWidget(server_group)

        # 控制按钮组
        control_group = QGroupBox("拍照控制")
        control_layout = QVBoxLayout(control_group)
        
        # 拍照按钮
        self.photo_btn = QPushButton("📸 拍照")
        self.photo_btn.setEnabled(False)
        self.photo_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        control_layout.addWidget(self.photo_btn)

        layout.addWidget(control_group)

        # 证件照设置组
        id_photo_group = QGroupBox("证件照设置")
        id_photo_layout = QVBoxLayout(id_photo_group)

        # 证件照设定按钮
        self.id_photo_settings_btn = QPushButton("⚙️ 证件照设定")
        self.id_photo_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        id_photo_layout.addWidget(self.id_photo_settings_btn)

        # 照片目录设置按钮
        self.photo_dir_btn = QPushButton("📁 照片目录")
        self.photo_dir_btn.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #546E7A;
            }
        """)
        id_photo_layout.addWidget(self.photo_dir_btn)

        layout.addWidget(id_photo_group)
        
        # 状态信息组
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        
        # 连接状态
        self.status_label = QLabel("状态: 未连接")
        status_layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(status_group)

        # 任务信息组（服务端模式时显示）
        self.task_group = QGroupBox("任务管理")
        task_layout = QVBoxLayout(self.task_group)

        # 当前任务信息显示
        current_task_label = QLabel("当前任务:")
        current_task_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        task_layout.addWidget(current_task_label)

        self.task_info_label = QLabel("暂无任务")
        self.task_info_label.setWordWrap(True)
        self.task_info_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                padding: 10px;
                border-radius: 4px;
                margin-bottom: 10px;
            }
        """)
        task_layout.addWidget(self.task_info_label)

        # 任务队列显示
        queue_label = QLabel("任务队列:")
        queue_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        task_layout.addWidget(queue_label)

        self.task_queue_list = QListWidget()
        self.task_queue_list.setMaximumHeight(100)
        self.task_queue_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #fafafa;
            }
        """)
        task_layout.addWidget(self.task_queue_list)

        # 任务操作按钮
        task_button_layout = QHBoxLayout()

        self.cancel_task_btn = QPushButton("取消任务")
        self.cancel_task_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        self.cancel_task_btn.setEnabled(False)
        task_button_layout.addWidget(self.cancel_task_btn)

        task_layout.addLayout(task_button_layout)

        # 拍照后操作按钮（初始隐藏）
        photo_action_layout = QHBoxLayout()

        self.retake_btn = QPushButton("取消重拍")
        self.retake_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        self.retake_btn.setVisible(False)
        photo_action_layout.addWidget(self.retake_btn)

        self.confirm_upload_btn = QPushButton("确认上传")
        self.confirm_upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.confirm_upload_btn.setVisible(False)
        photo_action_layout.addWidget(self.confirm_upload_btn)

        # 测试完成按钮（仅在测试模式下显示）
        self.test_complete_btn = QPushButton("测试完成")
        self.test_complete_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        self.test_complete_btn.setVisible(False)
        photo_action_layout.addWidget(self.test_complete_btn)

        task_layout.addLayout(photo_action_layout)

        self.task_group.setVisible(False)  # 初始隐藏
        layout.addWidget(self.task_group)

        # 日志输出
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # 清除日志按钮
        clear_log_btn = QPushButton("清除日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
        
    def setup_connections(self):
        """设置信号连接"""
        self.discover_btn.clicked.connect(self.show_device_selector)
        self.disconnect_btn.clicked.connect(self.disconnect_device)
        self.photo_btn.clicked.connect(self.take_photo)
        self.id_photo_settings_btn.clicked.connect(self.show_id_photo_settings)
        self.photo_dir_btn.clicked.connect(self.show_photo_directory_settings)

        # 服务端连接相关
        self.connect_server_btn.clicked.connect(self.show_server_selector)
        self.disconnect_server_btn.clicked.connect(self.disconnect_from_server)
        self.cancel_task_btn.clicked.connect(self.cancel_current_task)
        self.retake_btn.clicked.connect(self.retake_photo)
        self.confirm_upload_btn.clicked.connect(self.upload_photo)
        self.test_complete_btn.clicked.connect(self.complete_test_task)

        # 连接手机控制器的信号
        self.phone_controller.connected.connect(self.on_connected)
        self.phone_controller.disconnected.connect(self.on_disconnected)
        self.phone_controller.photo_taken.connect(self.on_photo_taken)
        self.phone_controller.error_occurred.connect(self.on_error)
        self.phone_controller.photo_confirmed.connect(self.on_photo_confirmed)
        self.phone_controller.photo_rejected.connect(self.on_photo_rejected)
        self.phone_controller.ready_to_receive.connect(self.on_mobile_ready_to_receive)

        # 连接服务端客户端的信号
        self.server_client.connected.connect(self.on_server_connected)
        self.server_client.disconnected.connect(self.on_server_disconnected)
        self.server_client.photo_settings_received.connect(self.on_photo_settings_received)
        self.server_client.task_received.connect(self.on_task_received)
        self.server_client.upload_result.connect(self.on_upload_result)
        self.server_client.error_occurred.connect(self.on_server_error)
        self.server_client.station_number_updated.connect(self.on_station_number_updated)
        self.server_client.task_deleted.connect(self.on_task_deleted)
        self.server_client.test_mode_changed.connect(self.on_test_mode_changed)
        self.phone_controller.log_message.connect(self.add_log)

        # 连接照片查看器的信号（用于服务端模式AI处理完成）
        self.photo_viewer.processing_finished.connect(self.on_photo_processing_finished)
        
    def show_device_selector(self):
        """显示设备选择器"""
        dialog = DeviceSelectorDialog(self)
        dialog.device_connected.connect(self.on_device_connected)
        dialog.exec_()

    def on_device_connected(self, ip: str, port: int):
        """设备连接回调 - 直接连接设备"""
        self.add_log(f"正在连接设备: {ip}:{port}")

        # 更新状态显示
        self.status_label.setText("状态: 连接中...")
        self.status_label.setStyleSheet("color: orange;")

        # 禁用连接按钮，防止重复连接
        self.discover_btn.setEnabled(False)

        # 开始连接
        self.phone_controller.connect_to_phone(ip, port)

    def disconnect_device(self):
        """断开设备连接"""
        if self.phone_controller.is_connected():
            self.phone_controller.disconnect()
            
    def take_photo(self):
        """拍照"""
        self.phone_controller.take_photo()

    def show_id_photo_settings(self):
        """显示证件照设置对话框"""
        dialog = PhotoSettingsDialog(self)
        dialog.settings_changed.connect(self.on_id_photo_settings_changed)
        if dialog.exec_() == PhotoSettingsDialog.Accepted:
            self.add_log("证件照设置已更新")

    def on_id_photo_settings_changed(self):
        """证件照设置更改回调"""
        self.add_log("证件照设置已保存")
        # 这里可以添加设置更改后的处理逻辑

    def show_photo_directory_settings(self):
        """显示照片目录设置对话框"""
        current_dir = self.phone_controller.get_photos_directory()

        # 显示当前目录信息
        stats = self.phone_controller.get_photo_stats()
        current_info = f"""当前目录: {current_dir}

目录结构:
├── raw/ (原始照片: {stats['raw_photos']}张)
└── id/ (证件照: {stats['id_photos']}张)

总计: {stats['total_photos']}张照片"""

        reply = QMessageBox.question(
            self, "照片目录设置",
            f"{current_info}\n\n是否要更改照片存储根目录？\n\n注意：更改后会在新目录下自动创建raw和id子目录",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            new_dir = QFileDialog.getExistingDirectory(
                self, "选择照片存储根目录", current_dir
            )

            if new_dir and new_dir != current_dir:
                if self.phone_controller.set_photos_directory(new_dir):
                    self.add_log(f"照片根目录已更改为: {new_dir}")
                    self.add_log(f"已创建子目录: {new_dir}/raw 和 {new_dir}/id")
                    # 更新照片查看器的目录
                    if hasattr(self, 'photo_viewer'):
                        self.photo_viewer.refresh_photos()
                else:
                    QMessageBox.warning(self, "设置失败", "无法设置新的照片目录")

    def get_photo_directory_info(self):
        """获取照片目录信息"""
        stats = self.phone_controller.get_photo_stats()
        return f"照片目录: {stats['directory']} (原始:{stats['raw_photos']}张, 证件照:{stats['id_photos']}张)"

    def on_connected(self):
        """连接成功回调"""
        self.status_label.setText("状态: 已连接")
        self.status_label.setStyleSheet("color: green;")
        self.discover_btn.setVisible(False)
        self.discover_btn.setEnabled(True)  # 恢复按钮状态
        self.disconnect_btn.setVisible(True)
        self.photo_btn.setEnabled(True)

        # 更新状态栏显示照片目录信息
        photo_info = self.get_photo_directory_info()
        self.statusBar().showMessage(f"已连接 | {photo_info}")
        self.add_log("设备连接成功")

        # 如果有当前任务，发送任务信息到手机
        if self.current_task:
            task_info_for_phone = {
                "name": self.current_task.get("name", ""),
                "gender": self.current_task.get("gender", ""),
                "id_number": self.current_task.get("id_number", ""),
                "task_id": self.current_task.get("task_id", "")
            }
            self.phone_controller.send_task_info(task_info_for_phone)
            self.add_log("当前任务信息已发送到手机")

    def on_disconnected(self):
        """断开连接回调"""
        self.status_label.setText("状态: 未连接")
        self.status_label.setStyleSheet("color: red;")
        self.discover_btn.setVisible(True)
        self.discover_btn.setEnabled(True)  # 恢复按钮状态
        self.disconnect_btn.setVisible(False)
        self.photo_btn.setEnabled(False)

        # 更新状态栏
        photo_info = self.get_photo_directory_info()
        self.statusBar().showMessage(f"未连接 | {photo_info}")
        self.add_log("设备已断开连接")
        
    def on_photo_taken(self, photo_path):
        """拍照成功回调"""
        self.add_log(f"拍照成功: {photo_path}")

        if self.server_mode and self.current_task:
            # 服务端模式：不保存本地照片，直接开始AI处理
            self.add_log("服务端模式：开始AI证件照处理...")
            self.start_ai_processing(photo_path)
        else:
            # 本地模式：在照片查看器中显示照片并自动开始AI处理
            self.photo_viewer.add_photo(photo_path)
            # 如果连接了手机，自动开始AI处理
            if self.phone_controller.is_connected():
                self.add_log("本地模式：开始AI证件照处理...")
                self.photo_viewer.process_id_photo()

    def start_ai_processing(self, photo_path):
        """开始AI证件照处理"""
        try:
            # 获取服务端照片参数并更新本地设置
            if self.server_client.photo_settings:
                self.add_log(f"服务端照片参数: {self.server_client.photo_settings}")
                self.update_local_photo_settings(self.server_client.photo_settings)
            else:
                self.add_log("警告：未获取到服务端照片参数，使用本地默认设置")

            # 添加照片到查看器
            self.photo_viewer.add_photo(photo_path)

            # 验证设置是否正确同步
            from core.photo_settings import get_photo_settings_manager
            settings_manager = get_photo_settings_manager()
            current_settings = settings_manager.get_current_settings()

            self.add_log(f"当前使用的设置:")
            self.add_log(f"  尺寸: {current_settings.size.name} ({current_settings.size.width}x{current_settings.size.height})")
            self.add_log(f"  背景: {current_settings.background_color.name} ({current_settings.background_color.hex_value})")
            self.add_log(f"  抠图模型: {current_settings.matting_model.name}")
            self.add_log(f"  DPI: {current_settings.dpi}, HD: {current_settings.hd}")

            # 使用原有的AI处理逻辑
            self.add_log("开始AI证件照处理...")
            self.photo_viewer.process_id_photo()

        except Exception as e:
            self.add_log(f"开始AI处理异常: {e}")
            QMessageBox.critical(self, "处理错误", f"开始AI处理时发生错误：\n{str(e)}")

    def on_photo_processing_finished(self, success, message):
        """照片处理完成回调"""
        if success:
            self.add_log(f"照片处理成功: {message}")

            # 在服务端模式下，发送处理后的照片到手机
            if self.server_client and self.server_client.connected:
                processed_photo_path = self.photo_viewer.last_processed_photo_path
                if processed_photo_path and os.path.exists(processed_photo_path):
                    self.add_log("发送处理后的照片到手机...")
                    if self.phone_controller:
                        self.phone_controller.send_processed_photo(processed_photo_path)
                        self.add_log("照片已发送到手机端")
                    else:
                        self.add_log("手机控制器未初始化")
                else:
                    self.add_log("未找到处理后的照片文件")
        else:
            self.add_log(f"照片处理失败: {message}")





    def update_local_photo_settings(self, server_settings):
        """根据服务端设置更新本地照片设置"""
        try:
            from core.photo_settings import get_photo_settings_manager, PhotoSize, BackgroundColor, MattingModel, FaceDetectModel
            settings_manager = get_photo_settings_manager()

            # 转换服务端设置格式为本地设置格式
            size_info = server_settings.get("size", {})
            bg_info = server_settings.get("background_color", {})
            matting_info = server_settings.get("matting_model", {})
            face_info = server_settings.get("face_detect_model", {})

            # 创建本地设置对象
            current_settings = settings_manager.get_current_settings()

            # 更新尺寸设置
            if size_info:
                width = size_info.get("width", current_settings.size.width)
                height = size_info.get("height", current_settings.size.height)
                size_name = size_info.get("name", "服务端设置")

                # 创建新的尺寸对象
                new_size = PhotoSize(size_name, width, height, "服务端同步设置")
                current_settings.size = new_size

            # 更新背景色设置
            if bg_info:
                bg_name = bg_info.get("name", "服务端背景")
                bg_hex = bg_info.get("hex_value", current_settings.background_color.hex_value)

                # 创建新的背景色对象
                new_bg = BackgroundColor(bg_name, bg_hex, "服务端同步设置")
                current_settings.background_color = new_bg

            # 更新抠图模型设置
            if matting_info:
                model_name = matting_info.get("name", "服务端抠图模型")
                model_id = matting_info.get("model_id", current_settings.matting_model.model_id)

                # 创建新的抠图模型对象
                new_matting = MattingModel(model_name, model_id, "服务端同步设置")
                current_settings.matting_model = new_matting

            # 更新人脸检测模型设置
            if face_info:
                face_name = face_info.get("name", "服务端人脸检测")
                face_model_id = face_info.get("model_id", current_settings.face_detect_model.model_id)

                # 创建新的人脸检测模型对象
                new_face = FaceDetectModel(face_name, face_model_id, "服务端同步设置")
                current_settings.face_detect_model = new_face

            # 更新其他设置
            current_settings.dpi = server_settings.get("dpi", current_settings.dpi)
            current_settings.hd = server_settings.get("hd", current_settings.hd)
            current_settings.face_alignment = server_settings.get("face_alignment", current_settings.face_alignment)
            current_settings.kb_size = server_settings.get("kb_size", current_settings.kb_size)
            current_settings.render_mode = server_settings.get("render_mode", current_settings.render_mode)

            # 保存更新后的设置
            settings_manager.save_settings()

            self.add_log(f"已同步服务端照片设置: {size_info.get('name', '未知尺寸')}, {bg_info.get('name', '未知背景')}")

        except Exception as e:
            self.add_log(f"更新本地照片设置失败: {e}")
            logger.error(f"更新本地照片设置异常: {e}")

    def on_ai_processing_complete(self, success, result_path, error_message=None):
        """AI处理完成回调"""
        if success and result_path:
            self.add_log("AI证件照处理完成")

            # 显示确认按钮
            self.retake_btn.setVisible(True)
            self.confirm_upload_btn.setVisible(True)
            self.add_log("请确认照片或选择重拍")
        else:
            error_msg = error_message or "AI处理失败"
            self.add_log(f"AI证件照处理失败: {error_msg}")
            QMessageBox.warning(self, "处理失败", f"AI证件照处理失败：\n{error_msg}")
        
    def on_error(self, error_msg):
        """错误回调"""
        self.add_log(f"错误: {error_msg}")

        # 如果是连接错误，恢复连接按钮状态
        if "连接" in error_msg:
            self.status_label.setText("状态: 连接失败")
            self.status_label.setStyleSheet("color: red;")
            self.discover_btn.setEnabled(True)

        QMessageBox.critical(self, "错误", error_msg)
        
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        # 自动滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    # 服务端连接相关方法
    def show_server_selector(self):
        """显示服务端选择对话框"""
        try:
            dialog = ServerSelectorDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                server_info = dialog.get_selected_server()
                if server_info:
                    self.connect_to_server(server_info)
        except Exception as e:
            self.add_log(f"显示服务端选择对话框异常: {e}")
            QMessageBox.critical(self, "错误", f"显示服务端选择对话框时发生错误：\n{str(e)}")

    def connect_to_server(self, server_info):
        """连接到服务端"""
        server_ip = server_info["server_ip"]
        server_port = server_info.get("server_port", 9090)  # 默认端口9090
        server_name = server_info.get("server_name", "未知服务端")

        self.add_log(f"正在连接服务端: {server_name} ({server_ip}:{server_port})")
        self.connect_server_btn.setEnabled(False)

        # 在线程中连接
        def connect_thread():
            success = self.server_client.connect_to_server(server_ip, server_port)
            if not success:
                self.connect_server_btn.setEnabled(True)

        threading.Thread(target=connect_thread, daemon=True).start()

    def disconnect_from_server(self):
        """断开服务端连接"""
        self.server_client.disconnect_from_server()

    def on_server_connected(self):
        """服务端连接成功"""
        self.server_mode = True
        self.add_log("已连接到服务端，进入服务端模式")

        # 更新UI状态
        self.connect_server_btn.setVisible(False)
        self.disconnect_server_btn.setVisible(True)
        self.server_ip_input.setEnabled(False)

        # 禁用本地照片参数设置
        self.id_photo_settings_btn.setEnabled(False)
        self.id_photo_settings_btn.setText("⚙️ 证件照设定 (服务端控制)")

        # 显示任务组
        self.task_group.setVisible(True)

        # 更新状态，显示机位号
        station_number = self.server_client.get_station_number()
        if station_number > 0:
            self.status_label.setText(f"状态: 已连接服务端 | 机位: {station_number}号")
            self.add_log(f"已分配到 {station_number} 号机位")
            # 更新窗口标题
            self.setWindowTitle(f"{config.APP_NAME} - {station_number}号机位")
        else:
            self.status_label.setText("状态: 已连接服务端")
            self.setWindowTitle(f"{config.APP_NAME} - 服务端模式")

        # 主动请求任务
        self.server_client.request_task()

    def on_server_disconnected(self):
        """服务端连接断开"""
        self.server_mode = False
        self.current_task = None
        self.task_queue.clear()  # 清空任务队列
        self.processing_task = False

        # 记录断开连接
        self.add_log("服务端连接已断开，切换到本地模式")

        # 更新UI状态
        self.connect_server_btn.setVisible(True)
        self.disconnect_server_btn.setVisible(False)
        self.server_ip_input.setEnabled(True)
        self.connect_server_btn.setEnabled(True)

        # 恢复本地照片参数设置
        self.id_photo_settings_btn.setEnabled(True)
        self.id_photo_settings_btn.setText("⚙️ 证件照设定")

        # 隐藏任务组
        self.task_group.setVisible(False)

        # 隐藏拍照后操作按钮
        self.retake_btn.setVisible(False)
        self.confirm_upload_btn.setVisible(False)
        self.test_complete_btn.setVisible(False)

        # 取消任务处理按钮
        self.cancel_task_btn.setEnabled(False)

        # 更新状态显示
        self.status_label.setText("状态: 本地模式")

        # 恢复窗口标题
        self.setWindowTitle(config.APP_NAME)

    def on_station_number_updated(self, old_station: int, new_station: int):
        """机位号更新回调"""
        try:
            self.add_log(f"机位号已更新: {old_station}号 -> {new_station}号")

            # 更新状态显示
            if self.server_mode:
                self.status_label.setText(f"状态: 已连接服务端 | 机位: {new_station}号")
                # 更新窗口标题
                self.setWindowTitle(f"{config.APP_NAME} - {new_station}号机位")

        except Exception as e:
            self.add_log(f"处理机位号更新异常: {e}")
            logger.error(f"处理机位号更新异常: {e}")

        # 清空任务显示
        self.task_info_label.setText("暂无任务")
        self.task_queue_list.clear()

        # 更新状态
        self.status_label.setText("状态: 未连接")

    def on_photo_settings_received(self, settings):
        """接收到照片参数设置"""
        self.add_log("已接收服务端照片参数设置")

        # 如果在服务端模式下，更新本地照片设置
        if self.server_mode:
            self.update_local_photo_settings(settings)
            self.add_log("照片参数已同步更新")

    def on_task_received(self, task):
        """接收到任务"""
        try:
            # 检查是否是重复任务
            task_id = task.get("task_id", "")
            for existing_task in self.task_queue:
                if existing_task.get("task_id") == task_id:
                    self.add_log(f"任务 {task_id} 已在队列中，跳过重复添加")
                    return

            # 检查当前任务是否是同一个
            if self.current_task and self.current_task.get("task_id") == task_id:
                self.add_log(f"任务 {task_id} 正在处理中，跳过重复添加")
                return

            # 添加任务到队列
            self.task_queue.append(task)

            name = task.get("name", "")
            id_number = task.get("id_number", "")

            self.add_log(f"收到新任务: {name} ({id_number})，已添加到队列 (队列长度: {len(self.task_queue)})")

            # 更新任务队列显示
            self.update_task_queue_display()

            # 如果当前没有在处理任务，开始处理下一个任务
            if not self.processing_task:
                self.process_next_task()

        except Exception as e:
            self.add_log(f"处理接收任务异常: {e}")
            logger.error(f"处理接收任务异常: {e}")

    def on_task_deleted(self, task_id):
        """收到任务删除通知"""
        try:
            self.add_log(f"收到任务删除通知: {task_id}")

            # 检查是否是当前正在处理的任务
            if self.current_task and self.current_task.get("task_id") == task_id:
                name = self.current_task.get("name", "")
                id_number = self.current_task.get("id_number", "")
                self.add_log(f"当前任务已被服务端删除: {name} ({id_number})")

                # 通知手机端任务已取消
                if self.phone_controller.is_connected():
                    self.add_log("通知手机端：任务已被服务端删除")
                    self.phone_controller.send_pc_action("PC_REJECTED")

                # 重置当前任务状态
                self.current_task = None
                self.processing_task = False
                self.cancel_task_btn.setEnabled(False)

                # 隐藏拍照后操作按钮
                self.retake_btn.setVisible(False)
                self.confirm_upload_btn.setVisible(False)
                self.test_complete_btn.setVisible(False)

                # 处理下一个任务（如果有的话）
                if self.task_queue:
                    self.update_task_queue_display()
                    self.process_next_task()
                else:
                    self.task_info_label.setText("等待新任务...")
                    self.task_queue_list.clear()
                    self.add_log("当前任务已删除，等待新任务")

                return

            # 检查任务队列中是否有该任务
            task_found = False
            for i, task in enumerate(self.task_queue):
                if task.get("task_id") == task_id:
                    name = task.get("name", "")
                    id_number = task.get("id_number", "")
                    self.add_log(f"队列中的任务已被服务端删除: {name} ({id_number})")

                    # 从队列中移除任务
                    self.task_queue.pop(i)
                    task_found = True
                    break

            if task_found:
                # 更新任务队列显示
                self.update_task_queue_display()
                self.add_log(f"任务已从队列中移除 (队列长度: {len(self.task_queue)})")
            else:
                self.add_log(f"未在本地队列中找到任务: {task_id}")

        except Exception as e:
            self.add_log(f"处理任务删除通知异常: {e}")
            logger.error(f"处理任务删除通知异常: {e}")

    def cancel_current_task(self):
        """取消当前任务"""
        if not self.current_task:
            return

        self.cancel_current_task_from_queue()

    def retake_photo(self):
        """取消重拍"""
        self.retake_btn.setVisible(False)
        self.confirm_upload_btn.setVisible(False)
        self.test_complete_btn.setVisible(False)
        self.add_log("已取消，可重新拍照")

        # 通知手机端PC端已取消
        if self.phone_controller.is_connected():
            self.add_log("通知手机端：PC端已取消")
            self.phone_controller.send_pc_action("PC_REJECTED")

    def upload_photo(self):
        """确认上传照片"""
        try:
            # 通知手机端PC端已确认
            if self.phone_controller.is_connected():
                self.add_log("通知手机端：PC端已确认")
                self.phone_controller.send_pc_action("PC_CONFIRMED")

            if not self.current_task:
                self.add_log("错误：没有当前任务")
                return

            # 获取当前照片数据
            current_photo = self.photo_viewer.get_current_photo_data()
            if not current_photo:
                QMessageBox.warning(self, "上传失败", "没有可上传的照片")
                self.add_log("错误：没有可上传的照片数据")
                return

            task_id = self.current_task.get("task_id")
            if not task_id:
                self.add_log("错误：任务ID为空")
                QMessageBox.warning(self, "上传失败", "任务ID无效")
                return

            self.add_log(f"正在上传照片... 任务ID: {task_id}, 照片大小: {len(current_photo)} 字节")
            self.confirm_upload_btn.setEnabled(False)

            # 生成照片文件名
            name = self.current_task.get("name", "unknown")
            id_number = self.current_task.get("id_number", "")
            photo_filename = f"{id_number}_{name}.jpg" if id_number else f"{task_id}.jpg"

            self.server_client.upload_photo(task_id, current_photo, photo_filename)

        except Exception as e:
            self.add_log(f"上传照片时出错: {e}")
            logger.error(f"上传照片异常: {e}")
            self.confirm_upload_btn.setEnabled(True)
            QMessageBox.critical(self, "上传错误", f"上传照片时发生错误：\n{str(e)}")

    def on_upload_result(self, success, message):
        """上传结果"""
        self.confirm_upload_btn.setEnabled(True)

        if success:
            self.add_log(f"照片上传成功: {message}")
            # 完成当前任务
            self.complete_current_task()
        else:
            self.add_log(f"照片上传失败: {message}")
            QMessageBox.warning(self, "上传失败", f"照片上传失败：\n{message}")

    def on_server_error(self, error_message):
        """服务端错误"""
        self.add_log(f"服务端错误: {error_message}")

        # 检查是否是连接断开相关的错误
        if "断开" in error_message or "连接" in error_message:
            # 连接相关错误，不显示弹窗，只记录日志
            logger.warning(f"服务端连接错误: {error_message}")
        else:
            # 其他错误，显示弹窗
            QMessageBox.warning(self, "服务端错误", error_message)

    def update_task_queue_display(self):
        """更新任务队列显示"""
        self.task_queue_list.clear()

        for i, task in enumerate(self.task_queue):
            name = task.get("name", "")
            id_number = task.get("id_number", "")
            status = "处理中" if i == 0 and self.processing_task else "等待中"

            item_text = f"{i+1}. {name} ({id_number}) - {status}"
            self.task_queue_list.addItem(item_text)

        # 向服务端报告当前任务数量
        if self.server_mode and self.server_client.connected_to_server:
            current_count = len(self.task_queue)
            # 如果有正在处理的任务但不在队列中，也要计算进去
            if self.processing_task and self.current_task and self.current_task not in self.task_queue:
                current_count += 1

            self.server_client.report_task_count(current_count)

    def on_test_mode_changed(self, is_test_mode, message):
        """测试模式变化处理"""
        self.test_mode = is_test_mode
        mode_text = "测试模式" if is_test_mode else "正式模式"
        self.add_log(f"服务端切换到: {mode_text}")

        # 更新窗口标题
        station_number = self.server_client.get_station_number()
        if station_number > 0:
            title_suffix = f"{station_number}号机位 - {mode_text}"
        else:
            title_suffix = f"服务端模式 - {mode_text}"
        self.setWindowTitle(f"{config.APP_NAME} - {title_suffix}")

        # 更新状态显示
        if station_number > 0:
            self.status_label.setText(f"状态: 已连接服务端 | 机位: {station_number}号 | {mode_text}")
        else:
            self.status_label.setText(f"状态: 已连接服务端 | {mode_text}")

    def complete_test_task(self):
        """完成测试任务"""
        if not self.test_mode:
            QMessageBox.warning(self, "错误", "当前不在测试模式")
            return

        if not self.current_task:
            QMessageBox.warning(self, "错误", "没有当前任务")
            return

        try:
            task_id = self.current_task.get("task_id")
            if not task_id:
                self.add_log("错误：任务ID为空")
                QMessageBox.warning(self, "完成失败", "任务ID无效")
                return

            self.add_log(f"正在完成测试任务... 任务ID: {task_id}")
            self.test_complete_btn.setEnabled(False)

            # 发送测试任务完成消息
            self.server_client.complete_test_task(task_id)

        except Exception as e:
            self.add_log(f"完成测试任务时出错: {e}")
            logger.error(f"完成测试任务异常: {e}")
            self.test_complete_btn.setEnabled(True)
            QMessageBox.critical(self, "完成错误", f"完成测试任务时发生错误：\n{str(e)}")

    def process_next_task(self):
        """处理下一个任务"""
        if not self.task_queue or self.processing_task:
            return

        # 取出队列中的第一个任务
        self.current_task = self.task_queue[0]
        self.processing_task = True

        # 显示当前任务信息
        name = self.current_task.get("name", "")
        gender = self.current_task.get("gender", "")
        id_number = self.current_task.get("id_number", "")

        # 构建任务信息，包含机位号
        task_info = f"姓名: {name}\n性别: {gender}\n身份证号: {id_number}"

        # 如果连接到服务端，显示机位号
        if self.server_mode:
            station_number = self.server_client.get_station_number()
            if station_number > 0:
                task_info += f"\n机位号: {station_number}号"

        self.task_info_label.setText(task_info)
        self.cancel_task_btn.setEnabled(True)

        # 更新队列显示
        self.update_task_queue_display()

        self.add_log(f"开始处理任务: {name} ({id_number})")

        # 如果是测试模式，直接显示测试完成按钮
        if self.test_mode:
            self.test_complete_btn.setVisible(True)
            self.add_log("测试模式：点击'测试完成'按钮来模拟任务完成")
        else:
            # 如果连接了手机，发送任务信息到手机
            if self.phone_controller.is_connected():
                task_info_for_phone = {
                    "name": name,
                    "gender": gender,
                    "id_number": id_number,
                    "task_id": self.current_task.get("task_id", "")
                }
                self.phone_controller.send_task_info(task_info_for_phone)
                self.add_log("任务信息已发送到手机")

    def complete_current_task(self):
        """完成当前任务"""
        try:
            if self.current_task:
                name = self.current_task.get("name", "")
                id_number = self.current_task.get("id_number", "")
                self.add_log(f"任务完成: {name} ({id_number})")

                # 从队列中移除已完成的任务（如果存在）
                if self.task_queue and self.current_task in self.task_queue:
                    self.task_queue.remove(self.current_task)
                elif self.task_queue:
                    # 如果当前任务不在队列中，移除第一个任务
                    self.task_queue.pop(0)

            # 重置状态
            self.current_task = None
            self.processing_task = False
            self.cancel_task_btn.setEnabled(False)

            # 隐藏拍照后操作按钮
            self.retake_btn.setVisible(False)
            self.confirm_upload_btn.setVisible(False)

            # 更新显示
            self.update_task_queue_display()

            # 处理下一个任务（如果有的话）
            if self.task_queue:
                # 延迟一点时间再处理下一个任务，避免过快切换
                QTimer.singleShot(1000, self.process_next_task)
            else:
                self.task_info_label.setText("等待新任务...")
                self.add_log("所有任务已完成，等待新任务")

        except Exception as e:
            self.add_log(f"完成任务时出错: {e}")
            logger.error(f"完成任务异常: {e}")

    def cancel_current_task_from_queue(self):
        """从队列中取消当前任务"""
        try:
            if self.current_task:
                name = self.current_task.get("name", "")
                id_number = self.current_task.get("id_number", "")

                # 向服务端发送取消请求
                task_id = self.current_task.get("task_id")
                if task_id:
                    success = self.server_client.cancel_task(task_id)
                    if success:
                        self.add_log(f"任务已取消: {name} ({id_number})")
                    else:
                        self.add_log(f"取消任务失败: {name} ({id_number})")

                # 从队列中移除当前任务（如果存在）
                if self.task_queue and self.current_task in self.task_queue:
                    self.task_queue.remove(self.current_task)
                elif self.task_queue:
                    # 如果当前任务不在队列中，移除第一个任务
                    self.task_queue.pop(0)

            # 重置状态
            self.current_task = None
            self.processing_task = False
            self.cancel_task_btn.setEnabled(False)

            # 隐藏拍照后操作按钮
            self.retake_btn.setVisible(False)
            self.confirm_upload_btn.setVisible(False)

            # 更新显示并处理下一个任务
            if self.task_queue:
                self.update_task_queue_display()
                self.process_next_task()
            else:
                self.task_info_label.setText("暂无任务")
                self.task_queue_list.clear()

        except Exception as e:
            self.add_log(f"取消任务时出错: {e}")
            logger.error(f"取消任务异常: {e}")

    def on_photo_processing_finished(self, success, message):
        """照片处理完成回调（服务端模式和本地模式）"""
        if success:
            self.add_log("AI证件照处理完成")

            # 照片发送由PhotoViewer的确认机制处理，这里不需要额外操作
            # 如果没有连接手机，显示PC端确认按钮
            if not self.phone_controller.is_connected():
                self.retake_btn.setVisible(True)
                self.confirm_upload_btn.setVisible(True)
                self.add_log("请在PC端确认照片或选择重拍")
            else:
                # 没有连接手机，显示PC端确认按钮
                self.retake_btn.setVisible(True)
                self.confirm_upload_btn.setVisible(True)
                self.add_log("请确认照片或选择重拍")
        else:
            self.add_log(f"AI证件照处理失败: {message}")
            # 不弹出警告对话框，只记录日志
            self.add_log("处理失败，请重新拍照")

    def on_photo_confirmed(self):
        """手机端确认照片"""
        self.add_log("手机端已确认照片")
        self.upload_photo()

    def on_photo_rejected(self):
        """手机端拒绝照片，需要重新拍照"""
        self.add_log("手机端拒绝照片，准备重新拍照")
        self.retake_photo()

    def on_mobile_ready_to_receive(self):
        """手机端确认准备接收照片，现在发送处理后的照片"""
        self.add_log("收到手机端确认，开始发送处理后的照片")
        if hasattr(self, 'photo_viewer') and self.photo_viewer:
            self.photo_viewer.send_processed_photo_to_mobile()
        else:
            self.add_log("错误：未找到照片查看器实例")
